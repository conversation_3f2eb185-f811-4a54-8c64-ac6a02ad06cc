package alerting_rule

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"sync"
	"text/template"

	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/mysql/alert_template"

	"github.com/VictoriaMetrics/metricsql"
	"icode.baidu.com/baidu/cprom/cloud-stack/cprom-common/utils"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/mysql/alerting_rule"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/mysql/model"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/mysql/vm_manage"
	"k8s.io/apimachinery/pkg/labels"

	"github.com/gin-gonic/gin"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"

	cpromv1 "icode.baidu.com/baidu/cprom/cloud-stack/pkg/apis/cprom/v1"
	vmv1beta1 "icode.baidu.com/baidu/cprom/cloud-stack/pkg/apis/vm/v1beta1"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/sts"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/usersetting"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/configuration"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/logger"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/middleware"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/models"
	"icode.baidu.com/baidu/cprom/cloud-stack/services/cprom-service/handler"
	//"github.com/VictoriaMetrics/VictoriaMetrics/app/vmalert/templates"
	//victoriametricsv1beta1 "github.com/VictoriaMetrics/operator/api/v1beta1"
	//v1 "k8s.io/api/core/v1"
)

const alertingRulePrefix = "alerting-"
const alertTemplatePrefix = "template-"

var templateMap = map[string]string{
	"$value":          ".Value",
	"$labels":         ".Labels",
	"$activeAt":       ".ActiveAt",
	"$alertID":        ".AlertID",
	"$groupID":        ".GroupID",
	"$expr":           ".Expr",
	"$for":            ".For",
	"$externalLabels": ".ExternalLabels",
	"$externalURL":    ".ExternalURL",
}

type AlertingRuleAPI struct {
	mgr            ctrl.Manager
	middlew        middleware.Interface
	config         *configuration.ServiceConfig
	templateConfig models.AlertTemplateConfig
	usersettingCli usersetting.Interface
	stsClient      sts.Interface

	clientSet client.Client
	once      sync.Once
}

// NewAlertingRuleApi 新建一个AlertingRuleApi实例，用于处理告警规则相关的请求
func NewAlertingRuleApi(cfg handler.Config) *AlertingRuleAPI {
	return &AlertingRuleAPI{
		mgr:            cfg.Mgr,
		middlew:        cfg.Middlew,
		config:         cfg.Config,
		usersettingCli: cfg.UserSetting,
		stsClient:      cfg.STSClient,
	}
}

// NewRegister 新建一个 AlertingRuleAPI 实例，用于处理 API Register 请求
func NewRegister(cfg handler.Config) handler.APIRegister {
	return &AlertingRuleAPI{
		mgr:            cfg.Mgr,
		middlew:        cfg.Middlew,
		config:         cfg.Config,
		usersettingCli: cfg.UserSetting,
		stsClient:      cfg.STSClient,
	}
}

func (h *AlertingRuleAPI) k8sClient(cache bool) client.Client {
	if cache {
		return h.mgr.GetClient()
	}

	h.once.Do(func() {
		k8sClient, err := client.New(h.mgr.GetConfig(), client.Options{
			Scheme: h.mgr.GetScheme(),
		})
		if err != nil {
			panic(err)
		}

		h.clientSet = k8sClient
	})

	return h.clientSet
}

func (h *AlertingRuleAPI) Create(c *gin.Context) {
	var (
		ctx        = handler.NewContext(c)
		res        = handler.NewResult()
		accountID  = c.MustGet("accountID").(string)
		instanceID = c.MustGet("instanceID").(string)
		//namespace  = instanceID
		m = vmv1beta1.VMRule{}
	)
	if err := c.Bind(&m); err != nil {
		err := fmt.Errorf("bind post data err: %v", err)
		logger.Errorf(ctx, "%v", err)

		res.Message = err.Error()
		c.JSON(http.StatusBadRequest, res)
		return
	}

	// 从cookie里获取用户名
	userName, err := c.Cookie("bce-login-display-name")
	totalCookie, err := c.Cookie("") // 获取所有cookie
	if err != nil {
		logger.Errorf(ctx, "[CreateAlertingRuleHandler] get userName from Cookie failed. totalCookie=[%v], errorInfo=[%v]", totalCookie, err.Error())
		// 兼容混合云调用接口没有传cookie的情况，不直接报错返回，指定userName为admin
		userName = "admin"
	}

	if m.Labels == nil {
		m.Labels = map[string]string{}
	}

	m.Labels[cpromv1.BCEAccountIDLabel] = accountID
	m.Labels[cpromv1.InstanceIDLabel] = instanceID // todo 0822 多租户中这里应该删掉，用accountId、projectId替代（因为迁移会导致instanceId变化）
	m.Labels[cpromv1.EnableLabel] = "true"
	m.Labels[cpromv1.VMRuleTypeLabel] = "alerting"

	alertingRuleID, err := utils.GenerateID(ctx, h.k8sClient(false),
		alertingRulePrefix, h.config.ResourceClusterRegionID,
		cpromv1.MonitorAlertNamespace, &vmv1beta1.VMRule{}, // 在monitor_alert所在的ns(cprom-alert)下生成唯一的alertingRuleId
	)
	if err != nil {
		res.Message = fmt.Sprintf("genAlertingRuleID err: %v", err)
		logger.Errorf(ctx, "%v", res.Message)

		c.JSON(http.StatusInternalServerError, res)
		return
	}
	m.Labels[cpromv1.AlertingRuleIDLabel] = alertingRuleID
	m.Name = alertingRuleID
	m.Namespace = cpromv1.MonitorAlertNamespace

	alertingRuleName := ""
	notifyRuleId := ""

	for _, group := range m.Spec.Groups {
		alertingRuleName = group.Name
		for _, rule := range group.Rules {

			// 校验rule是否是alerting rule
			if rule.Alert == "" {
				res.Message = fmt.Sprintf("input rule is not a valide alerting rule, rule:%v", rule)
				c.JSON(http.StatusBadRequest, res)
				return
			}

			//var notifyRuleNamespace = types.NamespacedName{
			//	// 一个子元素Rule关联一个notify rule
			//	Name:      rule.Labels[cpromv1.NotifyRuleIDLabel],
			//	Namespace: instanceID,
			//}

			notifyRuleId = rule.Labels[cpromv1.NotifyRuleIDLabel]
			if !h.validateNotifyRule(ctx, accountID, notifyRuleId) {
				res.Message = fmt.Sprintf("validate related notifyRule failed. "+
					"accountId=[%v], notifyRuleId=[%v]", accountID, notifyRuleId)
				c.JSON(http.StatusBadRequest, res)
				return
			}
			// add alert id label。
			// 放在alert中的内置标签，用于event-keeper查询告警规则使用
			rule.Labels[cpromv1.AccountIdLabel] = accountID
			rule.Labels[cpromv1.MonitorInstanceIdLabel] = instanceID
			rule.Labels[cpromv1.AlertSourceIDLabel] = alertingRuleID
			rule.Annotations[cpromv1.CalculateValueAnnotation] = "{{ $value }}" // 内置注解，用于记录promQL计算结果

			// 校验rule的expr、labels、annotations
			isValidate, errMsg := validateRule(rule)
			if !isValidate {
				res.Message = errMsg
				c.JSON(http.StatusBadRequest, res)
				return
			}
		}
	}

	// 0. 限制recording rule不得超过400个
	count, err := alerting_rule.CountById(instanceID)
	limit := 400
	if h.config.AlertRuleLimit > 0 {
		limit = h.config.AlertRuleLimit
	}
	if count > limit {
		res.Message = fmt.Sprintf("alert rule can not be larger than 400. isntanceId:%v err: %v", instanceID, err)
		logger.Errorf(ctx, "%v", res.Message)
		c.JSON(http.StatusTooManyRequests, res)
		return
	}

	// 1、持久化存储：将vmRule结构体转换为json字符串存入mysql
	vmRuleBytes, err := json.Marshal(&m) // 注意，此时m.Namespace为instanceID。目前针对双写情况ns=cprom-alert
	if err != nil {
		res.Message = fmt.Sprintf("json.Marshal alertingRule failed. namespace:%v, alertingRuleName:%v err: %v", m.Namespace, m.Name, err)
		logger.Errorf(ctx, "%v", res.Message)
		c.JSON(http.StatusBadRequest, res)
		return
	}
	alertingRuleRecord := model.AlertingRule{
		BCEAccountID:       accountID,
		NewInstanceID:      instanceID,
		AlertingRuleID:     alertingRuleID,
		AlertingRuleName:   alertingRuleName,
		AlertingRuleDetail: string(vmRuleBytes), // 此时的vmRuleBytes中不包含Create后m.Metadata中新填充的resourceVersion、selfLink等字段，以后也不会包含
		NotifyRuleID:       notifyRuleId,
		Operator:           userName,
	}
	err = alerting_rule.Insert(alertingRuleRecord)
	if err != nil {
		logger.Errorf(ctx, "[InsertAlertingRule] to DB failed. errorInfo=[%v]", err.Error())
		res.Message = fmt.Sprintf("insert record to DB failed.")
		c.JSON(http.StatusInternalServerError, res)
		return
	}

	// copy一份m，否则第一个ns下执行完Create后，m.Metadata中会被填充resourceVersion、selfLink等字段，无法在第二个ns下create
	mCopy := *(m.DeepCopy())

	// todo： this logic will remove finally
	manageAS := vm_manage.TAccountManage{}
	err = manageAS.Get(nil, instanceID)
	if err != nil {
		logger.Errorf(ctx, "[TAccountManage] to DB failed. errorInfo=[%v]", err.Error())
		res.Message = fmt.Sprintf("get data from manage acount DB failed.")
		c.JSON(http.StatusInternalServerError, res)
		return
	}

	/*
		if len(manageAS.FNewInstanceId) < 1 {
			// origin instance
			m.Namespace = instanceID
			if err = h.k8sClient(false).Create(ctx, &m); err != nil {  // 执行完Create后，m.Metadata中会被填充resourceVersion、selfLink等字段，
				// 如果创建失败，则对应删除mysql中的记录
				res.Message = fmt.Sprintf("create AlertingRule: %v/%v err: %v", m.Namespace, m.Name, err)
				logger.Errorf(ctx, "%v", res.Message)
				c.JSON(http.StatusBadRequest, res)
				return
			}
			logger.Infof(ctx, "create AlertingRule: %v/%v successful", m.Namespace, m.Name)
		}
	*/

	// 2.2 创建cprom-alert下的alertingRule
	mCopy.Namespace = cpromv1.MonitorAlertNamespace
	if err = h.k8sClient(false).Create(ctx, &mCopy); err != nil {
		res.Message = fmt.Sprintf("create AlertingRule: %v/%v err: %v", mCopy.Namespace, mCopy.Name, err)
		logger.Errorf(ctx, "%v", res.Message)
		c.JSON(http.StatusBadRequest, res)
		return
	}
	logger.Infof(ctx, "create AlertingRule: %v/%v successful", mCopy.Namespace, mCopy.Name)

	res.Result = gin.H{"AlertingRuleID": m.Name}
	c.JSON(http.StatusOK, res)

}

func (h *AlertingRuleAPI) Validate(c *gin.Context) {
	var (
		res = handler.NewResult()
		ctx = handler.NewContext(c)
		m   = make(map[string]string)
	)
	if err := c.Bind(&m); err != nil {
		res.Message = fmt.Sprintf("bind post data err: %v", err)
		logger.Errorf(ctx, "%v", res.Message)

		c.JSON(http.StatusBadRequest, res)
		return
	}
	// 解析模板字符串
	tmpl := m["description"]
	for k, v := range templateMap {
		tmpl = strings.ReplaceAll(tmpl, k, v)
	}
	if _, err := template.New("example").Parse(tmpl); err != nil {
		res.Message = fmt.Sprintf("validate failed, err: %s", err.Error())
		logger.Errorf(ctx, "%v", res.Message)
	}
	c.JSON(http.StatusOK, res)
	return
}

func (h *AlertingRuleAPI) Update(c *gin.Context) {
	var (
		ctx                = handler.NewContext(c)
		res                = handler.NewResult()
		accountID          = c.MustGet("accountID").(string)
		instanceID         = c.MustGet("instanceID").(string)
		alertingRuleID     = c.MustGet("alertingRuleID").(string)
		alertingRuleRecord = c.MustGet("alertingRuleRecord").(*model.AlertingRule)
		//alertingRuleInstance, exist   	= c.Get("alertingRuleInstance")  // ns=监控实例Id查询的vmRule，其中包含了Metadata.resourceVersion、selfLink等k8s自动生成的字段
		alertingRule = c.MustGet("alertingRule").(*vmv1beta1.VMRule) // ns=cprom-alert查询的vmRule，其中包含了Metadata.resourceVersion、selfLink等k8s自动生成的字段
		mu           = vmv1beta1.VMRule{}
	)
	if err := c.Bind(&mu); err != nil {
		res.Message = fmt.Sprintf("bind post data err: %v", err)
		logger.Errorf(ctx, "%v", res.Message)

		c.JSON(http.StatusBadRequest, res)
		return
	}

	// 从cookie里获取用户名
	userName, err := c.Cookie("bce-login-display-name")
	totalCookie, err := c.Cookie("") // 获取所有cookie
	if err != nil {
		logger.Errorf(ctx, "[UpdateAlertingRuleHandler] get userName from Cookie failed. totalCookie=[%v], errorInfo=[%v]", totalCookie, err.Error())
		// 兼容混合云调用接口没有传cookie的情况，不直接报错返回，指定userName为admin
		userName = "admin"
	}

	// 0、将从mysql中查询的记录转化为k8s的结构，
	m := vmv1beta1.VMRule{} // alertingRule的旧记录
	err = json.Unmarshal([]byte(alertingRuleRecord.AlertingRuleDetail), &m)
	if err != nil {
		logger.Errorf(ctx, "json.unmarshal for alertingRuleRecord.AlertingRuleDetail failed. errorInfo=[%v]", err.Error())
		res.Message = fmt.Sprintf("update alertingRule failed. erroInfo=[json.unmarshal record from DB failed]")
		c.JSON(http.StatusInternalServerError, res)
		return
	}

	// 用待更新的alertingRule配置替换旧记录的部分字段
	m.Spec = mu.Spec

	// 更新Enable Label
	if v, ok := mu.Labels[cpromv1.EnableLabel]; ok {
		m.Labels[cpromv1.EnableLabel] = v
		//alertingRuleInstance.Labels[cpromv1.EnableLabel] = v
		alertingRule.Labels[cpromv1.EnableLabel] = v
	}

	alertingRuleName := ""
	notifyRuleId := ""

	// update时也要与create保持一致，默认增加内置标签指定alerting_rule_id
	// 对update的alertingRule也增加通知策略的校验 -> 与create保持一致
	for _, group := range m.Spec.Groups {
		alertingRuleName = group.Name
		for _, rule := range group.Rules {
			// 校验rule是否是alerting rule
			if rule.Alert == "" {
				res.Message = fmt.Sprintf("update rule is not a valide alerting rule, rule:%v", rule)
				c.JSON(http.StatusBadRequest, res)
				return
			}

			//var notifyRuleNamespace = types.NamespacedName{
			//	// 一个子元素Rule关联一个notify rule
			//	Name:      rule.Labels[cpromv1.NotifyRuleIDLabel],
			//	Namespace: instanceID,
			//}

			notifyRuleId = rule.Labels[cpromv1.NotifyRuleIDLabel]
			if !h.validateNotifyRule(ctx, accountID, notifyRuleId) {
				res.Message = fmt.Sprintf("validate related notifyRule failed. "+
					"accountId=[%v], notifyRuleId=[%v]", accountID, notifyRuleId)
				c.JSON(http.StatusBadRequest, res)
				return
			}

			// 放在alert中的内置标签，用于event-keeper查询告警规则使用
			rule.Labels[cpromv1.AccountIdLabel] = accountID
			rule.Labels[cpromv1.MonitorInstanceIdLabel] = instanceID // todo 0822 多租户中这里应该删掉，用accountId、projectId替代（因为迁移会导致instanceId变化）
			rule.Labels[cpromv1.AlertSourceIDLabel] = alertingRuleID
			rule.Annotations[cpromv1.CalculateValueAnnotation] = "{{ $value }}" // 内置注解，用于记录promQL计算结果

			// 校验rule的expr、labels、annotations
			isValidate, errMsg := validateRule(rule)
			if !isValidate {
				res.Message = errMsg
				c.JSON(http.StatusBadRequest, res)
				return
			}
		}
	}

	// 1、持久化存储：将vmRule结构体转换为json字符串存入mysql
	vmRuleBytes, err := json.Marshal(&m) // 注意，此时m.Namespace为instanceID。目前针对双写情况ns=cprom-alert
	if err != nil {
		res.Message = fmt.Sprintf("json.Marshal alertingRule failed. namespace:%v, alertingRuleName:%v err: %v", m.Namespace, m.Name, err)
		logger.Errorf(ctx, "%v", res.Message)
		c.JSON(http.StatusBadRequest, res)
		return
	}
	newAlertingRuleRecord := model.AlertingRule{
		BCEAccountID:       accountID,
		NewInstanceID:      instanceID,
		AlertingRuleID:     alertingRuleID,
		AlertingRuleName:   alertingRuleName,
		AlertingRuleDetail: string(vmRuleBytes),
		NotifyRuleID:       notifyRuleId,
		Operator:           userName,
	}
	err = alerting_rule.Update(newAlertingRuleRecord)
	if err != nil {
		logger.Errorf(ctx, "[UpdateAlertingRule] to DB failed. errorInfo=[%v]", err.Error())
		res.Message = fmt.Sprintf("update record to DB failed.")
		c.JSON(http.StatusInternalServerError, res)
		return
	}
	logger.Infof(ctx, "update alertingRule in DB successful")

	/*
		if exist {
			alertingRuleInstance := alertingRuleInstance.(*vmv1beta1.VMRule)
			alertingRuleInstance.Spec = m.Spec
			if err = h.k8sClient(false).Update(ctx, alertingRuleInstance); err != nil {
				res.Message = fmt.Sprintf("update alertingRule failed. %v/%v err: %v", alertingRuleInstance.Namespace, alertingRuleInstance.Name, err)
				logger.Errorf(ctx, "%v", res.Message)
				c.JSON(http.StatusInternalServerError, res)
				return
			}
			logger.Infof(ctx, "update alertingRule: %v/%v successful", alertingRuleInstance.Namespace, alertingRuleInstance.Name)
		}

	*/

	// 2.2 更新cprom-alert下的alertingRule
	alertingRule.Spec = m.Spec
	if err = h.k8sClient(false).Update(ctx, alertingRule); err != nil {
		res.Message = fmt.Sprintf("update alertingRule failed. %v/%v err: %v", alertingRule.Namespace, alertingRule.Name, err)
		logger.Errorf(ctx, "%v", res.Message)
		c.JSON(http.StatusInternalServerError, res)
		return
	}
	logger.Infof(ctx, "update alertingRule: %v/%v successful", alertingRule.Namespace, alertingRule.Name)

	c.JSON(http.StatusOK, res)
	return
}

func (h *AlertingRuleAPI) Delete(c *gin.Context) {
	var (
		ctx            = handler.NewContext(c)
		res            = handler.NewResult()
		instanceID     = c.MustGet("instanceID").(string)
		alertingRuleID = c.MustGet("alertingRuleID").(string)
		//alertingRuleInstance, exist = c.Get("alertingRuleInstance")  // ns=监控实例Id查询的vmRule
		alertingRule = c.MustGet("alertingRule").(*vmv1beta1.VMRule) // ns=cprom-alert查询的vmRule
		accountID    = c.MustGet("accountID").(string)
	)

	/*
		if exist {
			alertingRuleInstance := alertingRuleInstance.(*vmv1beta1.VMRule)
			if err := h.k8sClient(false).Delete(ctx, alertingRuleInstance); err != nil {
				res.Message = fmt.Sprintf("delete alertingRule failed. %v/%v err: %v", alertingRuleInstance.Namespace, alertingRuleInstance.Name, err)
				logger.Errorf(ctx, "%v", res.Message)
				c.JSON(http.StatusInternalServerError, res)
				return
			}
			logger.Infof(ctx, "delete alertingRule: %v/%v successful", alertingRuleInstance.Namespace, alertingRuleInstance.Name)
		}

	*/

	// 1.2 删除cprom-alert下的alertingRule
	if err := h.k8sClient(false).Delete(ctx, alertingRule); err != nil {
		res.Message = fmt.Sprintf("delete alertingRule failed. %v/%v err: %v", alertingRule.Namespace, alertingRule.Name, err)
		logger.Errorf(ctx, "%v", res.Message)
		c.JSON(http.StatusInternalServerError, res)
		return
	}
	logger.Infof(ctx, "delete alertingRule: %v/%v successful", alertingRule.Namespace, alertingRule.Name)

	// 2、删除alertingRuleId在alertingRule表中对应的记录
	err := alerting_rule.Delete(alertingRuleID, instanceID)
	if err != nil {
		logger.Errorf(ctx, "[DeleteAlertingRule] to DB failed. errorInfo=[%v]", err.Error())
		res.Message = fmt.Sprintf("delete record to DB failed.")
		c.JSON(http.StatusInternalServerError, res)
		return
	}

	err = alert_template.DeleteMappingByAlertIDAndAccountID(alertingRuleID, accountID)
	if err != nil {
		logger.Errorf(ctx, "[DeleteAlertingRuleMapping] to DB failed. errorInfo=[%v]", err.Error())
	}

	logger.Infof(ctx, "delete alertingRule in DB successful")

	c.JSON(http.StatusOK, res)
	return
}

func (h *AlertingRuleAPI) Get(c *gin.Context) {
	var (
		ctx = handler.NewContext(c)
		res = handler.NewResult()
		//instanceID     = c.MustGet("instanceID").(string)
		alertingRuleID     = c.MustGet("alertingRuleID").(string)
		alertingRuleRecord = c.MustGet("alertingRuleRecord").(*model.AlertingRule)
	)

	logger.Infof(ctx, "get alertingRuleID=[%v], alertingRule=[%v]", alertingRuleID, alertingRuleRecord)

	// 1、将AlertingRuleDetail部分转化为结构体
	m := vmv1beta1.VMRule{}
	err := json.Unmarshal([]byte(alertingRuleRecord.AlertingRuleDetail), &m)
	if err != nil {
		logger.Errorf(ctx, "JsonUnmarshal for alertingRuleRecord.AlertingRuleDetail failed. errorInfo=[%v]", err.Error())
		res.Message = fmt.Sprintf("get record from DB failed.")
		c.JSON(http.StatusInternalServerError, res)
		return
	}

	// 2、按照原有处理方式处理m结构
	HideFields4AR(&m)

	severity, ok := m.Spec.Groups[0].Rules[0].Labels[cpromv1.SeverityLabel]
	if !ok || severity == "" { // 老策略中没有SeverityLabel这个值或为空，给一个内置值
		m.Spec.Groups[0].Rules[0].Labels[cpromv1.SeverityLabel] = "notice"
	}

	notifyRuleID, ok := m.Spec.Groups[0].Rules[0].Labels["cprom_notify_rule_id"]
	if ok && notifyRuleID == "notify-default" {
		m.Spec.Groups[0].Rules[0].Labels["cprom_notify_rule_id"] = ""
	}

	res.Result = m

	c.JSON(http.StatusOK, res)
	return
}

func (h *AlertingRuleAPI) List(c *gin.Context) {
	var (
		ctx = handler.NewContext(c)
		//accountID   = c.MustGet("accountID").(string)
		instanceID  = c.MustGet("instanceID").(string)
		res         = handler.NewResult()
		pageNo      = c.GetInt("pageNo")
		pageSize    = c.GetInt("pageSize")
		keywordType = c.Query("keywordType") // keywordType 支持：AlertName
		keyword     = c.Query("keyword")
	)

	// 1、根据pageSize、pageNo、instanceID从mysql查对应的告警规则列表
	alertingRuleName := ""
	if keywordType == "AlertName" {
		alertingRuleName = keyword
	}
	pageSizeInt64 := int64(pageSize)
	pageNoInt64 := int64(pageNo)
	totalCount, alertingRuleRecords, err := alerting_rule.QueryLimitByPageSize(alertingRuleName, instanceID, pageSizeInt64, pageNoInt64)
	if err != nil {
		res.Message = fmt.Sprintf("batch query alertingRecords from DB failed.")
		logger.Errorf(ctx, "[QueryLimitByPageSize] from DB failed. errorInfo=%s", err.Error())
		c.JSON(http.StatusInternalServerError, res)
		return
	}

	items := make([]*vmv1beta1.VMRule, 0)
	for _, alertingRuleRecord := range alertingRuleRecords { // alertingRuleRecords已经按照updateTime降序排列过了
		// 1、将AlertingRuleDetail部分转化为结构体
		m := vmv1beta1.VMRule{}
		err = json.Unmarshal([]byte(alertingRuleRecord.AlertingRuleDetail), &m)
		if err != nil {
			logger.Errorf(ctx, "JsonUnmarshal for alertingRuleRecord.AlertingRuleDetail failed. "+
				"aleringRuleId=[%v], errorInfo=[%v]", alertingRuleRecord.AlertingRuleID, err.Error())
			continue
		}

		// 2、依次处理每个alertingRule
		// 2.1 隐藏内置字段
		HideFields4AR(&m)

		// 2.2 兼容老数据，对老策略labels里没有SeverityLabel字段的设置默认值为notice
		severity, ok := m.Spec.Groups[0].Rules[0].Labels[cpromv1.SeverityLabel]
		if !ok || severity == "" { // 老策略中没有SeverityLabel这个值或为空，给一个内置值
			m.Spec.Groups[0].Rules[0].Labels[cpromv1.SeverityLabel] = "notice"
		}

		notifyRuleID, ok := m.Spec.Groups[0].Rules[0].Labels["cprom_notify_rule_id"]
		if ok && notifyRuleID == "notify-default" {
			m.Spec.Groups[0].Rules[0].Labels["cprom_notify_rule_id"] = ""
		}

		items = append(items, &m)
	}

	listResp := models.ListResponse{
		KeywordType: keywordType,
		Keyword:     keyword,
		PageNo:      pageNo,
		PageSize:    pageSize,
		TotalCount:  totalCount,
		Items:       items,
	}
	res.Result = listResp

	c.JSON(http.StatusOK, res)
}

func (h *AlertingRuleAPI) GetForBackend(c *gin.Context) {
	var (
		ctx                = handler.NewContext(c)
		res                = handler.NewResult()
		instanceID         = c.MustGet("instanceID").(string)
		alertingRuleID     = c.MustGet("alertingRuleID").(string)
		alertingRuleRecord = c.MustGet("alertingRuleRecord").(*model.AlertingRule)
	)

	logger.Infof(ctx, "get alertingRuleID=[%v], alertingRule=[%v]", alertingRuleID, alertingRuleRecord)

	// 1、将AlertingRuleDetail部分转化为结构体
	m := vmv1beta1.VMRule{}
	err := json.Unmarshal([]byte(alertingRuleRecord.AlertingRuleDetail), &m)
	if err != nil {
		logger.Errorf(ctx, "JsonUnmarshal for alertingRuleRecord.AlertingRuleDetail failed. errorInfo=[%v]", err.Error())
		res.Message = fmt.Sprintf("get record from DB failed.")
		c.JSON(http.StatusInternalServerError, res)
		return
	}

	// 3、按照原有逻辑处理
	HideFields4AR(&m)

	alertingRuleSpec := m.Spec.Groups[0]

	var severity string
	var description string
	var notifyRuleId string
	var ok bool
	if severity, ok = alertingRuleSpec.Rules[0].Labels[cpromv1.SeverityLabel]; ok {
		severity = alertingRuleSpec.Rules[0].Labels[cpromv1.SeverityLabel]
		delete(alertingRuleSpec.Rules[0].Labels, cpromv1.SeverityLabel)
	}
	if !ok || severity == "" { // 老策略中没有SeverityLabel这个值或为空，给一个内置值
		severity = "notice"
	}
	if notifyRuleId, ok = alertingRuleSpec.Rules[0].Labels[cpromv1.NotifyRuleIDLabel]; ok { // 从labels中删除标记通知策略id的标签，这不属于用户配置的labels
		notifyRuleId = alertingRuleSpec.Rules[0].Labels[cpromv1.NotifyRuleIDLabel]
		delete(alertingRuleSpec.Rules[0].Labels, cpromv1.NotifyRuleIDLabel)
	}
	if description, ok = alertingRuleSpec.Rules[0].Annotations[cpromv1.DescriptionAnnotation]; ok {
		description = alertingRuleSpec.Rules[0].Annotations[cpromv1.DescriptionAnnotation]
		delete(alertingRuleSpec.Rules[0].Annotations, cpromv1.DescriptionAnnotation)
	}

	res.Result = cpromv1.AlertingRule{
		MonitorInstanceId: instanceID,
		AlertingRuleId:    alertingRuleID,
		AlertingRuleName:  alertingRuleSpec.Rules[0].Alert,
		Expr:              alertingRuleSpec.Rules[0].Expr.String(),
		Severity:          severity,
		Duration:          alertingRuleSpec.Rules[0].For,
		Description:       description,
		Labels:            alertingRuleSpec.Rules[0].Labels, // 只保留用户配置的labels，所有其他系统增加的labels一律需要剔除
		Annotations:       alertingRuleSpec.Rules[0].Annotations,
		NotifyRuleId:      notifyRuleId,
	}

	c.JSON(http.StatusOK, res)
	return
}

func (h *AlertingRuleAPI) DeleteAlertingRuleForInstance(ctx context.Context, accountId string, monitorInstanceId string) string {
	var (
		errorInfo  = ""
		instanceID = monitorInstanceId // monitorAlert对应的监控实例id
		accountID  = accountId
	)
	// 删除mysql里 + cprom-alert下监控实例相关的alertingRule

	// 1、删除cprom-alert下监控实例相关的alertingRule
	alertingRules := vmv1beta1.VMRule{}

	selector := fmt.Sprintf("%s=%s,%s=%s,%s=%s",
		cpromv1.BCEAccountIDLabel, accountID,
		cpromv1.InstanceIDLabel, instanceID,
		cpromv1.VMRuleTypeLabel, "alerting",
	)
	labelSelector, err := labels.Parse(selector)

	deleteOpts := client.DeleteAllOfOptions{
		ListOptions: client.ListOptions{
			Namespace:     cpromv1.MonitorAlertNamespace,
			LabelSelector: labelSelector,
		},
	}

	if err := h.k8sClient(false).DeleteAllOf(ctx, &alertingRules, &deleteOpts); err != nil {
		logger.Errorf(ctx, "[BatchDeleteAlertingRule] in %v/%v/%v failed. errorInfo=[%v]", cpromv1.MonitorAlertNamespace, accountID, instanceID, err.Error())
		errorInfo = fmt.Sprintf("batch delete alertingRules of %v/%v/%v failed.", cpromv1.MonitorAlertNamespace, accountID, instanceID)
		return errorInfo
	}
	logger.Infof(ctx, "batch delete alertingRules of %v/%v/%v successful", cpromv1.MonitorAlertNamespace, accountID, instanceID)

	// 2、删除mysql中instanceId对应的记录
	err = alerting_rule.BatchDelete(instanceID, accountID)
	if err != nil {
		logger.Errorf(ctx, "[BatchDeleteAlertingRule] from DB failed. %v/%v, errorInfo=[%v]", accountID, instanceID, err.Error())
		errorInfo = fmt.Sprintf("batch delete alertingRules of %v/%v failed.", accountID, instanceID)
		return errorInfo
	}

	err = alert_template.BatchDeleteMappingsByInstanceID(instanceID, accountID)
	if err != nil {
		logger.Errorf(ctx, "[BatchDeleteAlertTemplateMapping] from DB failed. %v/%v, errorInfo=[%v]", accountID, instanceID, err.Error())
		errorInfo = fmt.Sprintf("batch delete AlertTemplateMapping of %v/%v failed.", accountID, instanceID)
		return errorInfo
	}

	logger.Infof(ctx, "batch delete alertingRules of %v/%v from DB successful.", accountID, instanceID)
	return ""
}

// 迁移存量alertingRule使用的方法
func (h *AlertingRuleAPI) Migrate(ctx context.Context, accountId string, monitorInstanceId string) string {
	var (
		accountID  = accountId
		instanceID = monitorInstanceId
		userName   = "work"
		errorInfo  = ""
	)

	errorRuleList := make([]string, 0)

	// 1、查询监控实例下所有的alertingRule
	var mm vmv1beta1.VMRuleList

	selector := fmt.Sprintf("%s=%s,%s=%s,%s=%s",
		cpromv1.BCEAccountIDLabel, accountID,
		cpromv1.InstanceIDLabel, instanceID,
		cpromv1.VMRuleTypeLabel, "alerting",
	)
	labelSelector, err := labels.Parse(selector)
	if err != nil {
		errorInfo = fmt.Sprintf("parse labels: %v err: %v", selector, err)
		logger.Errorf(ctx, "%v", errorInfo)
		return errorInfo
	}

	if err := h.k8sClient(false).List(ctx, &mm, &client.ListOptions{
		Namespace:     instanceID,
		LabelSelector: labelSelector,
	}); err != nil {
		errorInfo = fmt.Sprintf("list %s err: %v", selector, err)
		logger.Errorf(ctx, "%v", errorInfo)
		return errorInfo
	}

	// 2、依次处理各个alertingRule
	for i := range mm.Items {
		alertingRuleID := mm.Items[i].Name

		m := vmv1beta1.VMRule{}

		if m.Labels == nil {
			m.Labels = map[string]string{}
		}
		m.Labels[cpromv1.BCEAccountIDLabel] = accountID
		m.Labels[cpromv1.InstanceIDLabel] = instanceID
		m.Labels[cpromv1.EnableLabel] = "true"
		m.Labels[cpromv1.VMRuleTypeLabel] = "alerting"
		m.Labels[cpromv1.AlertingRuleIDLabel] = alertingRuleID

		m.Name = alertingRuleID
		m.Namespace = cpromv1.MonitorAlertNamespace

		m.Spec = mm.Items[i].Spec

		alertingRuleName := ""
		notifyRuleId := ""
		for _, group := range m.Spec.Groups {
			alertingRuleName = group.Name
			for _, rule := range group.Rules {
				notifyRuleId = rule.Labels["cprom_notify_rule_id"]
				break
			}
		}

		// 2.1 将alertingRule写入mysql
		vmRuleBytes, err := json.Marshal(&m) // 注意，此时m.Namespace为instanceID。目前针对双写情况ns=cprom-alert
		if err != nil {
			errorInfo = fmt.Sprintf("json.Marshal alertingRule failed. namespace:%v, alertingRuleName:%v err: %v", m.Namespace, m.Name, err)
			logger.Errorf(ctx, "%v", errorInfo)
			errorRuleList = append(errorRuleList, alertingRuleID)
		}
		alertingRuleRecord := model.AlertingRule{
			BCEAccountID:       accountID,
			NewInstanceID:      instanceID,
			AlertingRuleID:     alertingRuleID,
			AlertingRuleName:   alertingRuleName,
			AlertingRuleDetail: string(vmRuleBytes), // 此时的vmRuleBytes中不包含Create后m.Metadata中新填充的resourceVersion、selfLink等字段，以后也不会包含
			NotifyRuleID:       notifyRuleId,
			Operator:           userName,
			IsDelete:           1,
		}
		err = alerting_rule.Insert(alertingRuleRecord)
		if err != nil {
			errorInfo = fmt.Sprintf("[InsertAlertingRule] to DB failed. errorInfo=[%v]", err.Error())
			logger.Errorf(ctx, "%v", errorInfo)
			errorRuleList = append(errorRuleList, alertingRuleID)
		}

		// 2.2 将alertingRule写入ns=cprom-alert
		if err = h.k8sClient(false).Create(ctx, &m); err != nil { // 执行完Create后，m.Metadata中会被填充resourceVersion、selfLink等字段，
			// 如果创建失败，则对应删除mysql中的记录
			errorInfo = fmt.Sprintf("migrate AlertingRule: %v/%v err: %v", m.Namespace, m.Name, err)
			logger.Errorf(ctx, "%v", errorInfo)
			errorRuleList = append(errorRuleList, alertingRuleID)
		}
		logger.Infof(ctx, "migrate AlertingRule: %v/%v successful", m.Namespace, m.Name)
	}
	return strings.Join(errorRuleList, ",")
}

/* 暂时不需要
func (h *AlertingRuleAPI) ListTotal(c *gin.Context) {
	var (
		ctx         = handler.NewContext(c)
		res         = handler.NewResult()
		accountID   = c.MustGet("accountID").(string)
	)

	totalInstanceIdList, err := h.getTotalInstanceIdList(ctx, accountID)  // 资源账号下所有的监控实例id
	if err != nil {
		res.Message = fmt.Sprintf("getTotalInstanceIdList failed. err: %v", err)
		logger.Errorf(ctx, "%v", res.Message)
		c.JSON(http.StatusInternalServerError, res)
		return
	}
	totalAlertingRuleNameList := make([]string, 0)  // 所有资源账号下的告警规则名

	for _, instanceID := range totalInstanceIdList {
		selector := fmt.Sprintf("%s=%s,%s=%s,%s=%s",
			cpromv1.BCEAccountIDLabel, accountID,
			cpromv1.InstanceIDLabel, instanceID,
			cpromv1.VMRuleTypeLabel, "alerting",
		)

		labelSelector, err := labels.Parse(selector)
		if err != nil {
			res.Message = fmt.Sprintf("parse labels: %v err: %v", selector, err)
			logger.Errorf(ctx, "%v", res.Message)

			c.JSON(http.StatusInternalServerError, res)
			return
		}

		var mm vmv1beta1.VMRuleList

		if err := h.k8sClient(false).List(ctx, &mm, &client.ListOptions{
			Namespace:     instanceID,
			LabelSelector: labelSelector,
		}); err != nil {
			res.Message = fmt.Sprintf("list %s err: %v", selector, err)
			logger.Errorf(ctx, "%v", res.Message)

			c.JSON(http.StatusInternalServerError, res)
			return
		}

		for _, item := range mm.Items {
			totalAlertingRuleNameList = append(totalAlertingRuleNameList, item.Spec.Groups[0].Name)
		}
	}

	listResp := models.ListResponse{
		TotalCount:  len(totalAlertingRuleNameList),
	}

	listResp.Items = totalAlertingRuleNameList
	res.Result = listResp

	c.JSON(http.StatusOK, res)
}

func (h *AlertingRuleAPI) getTotalInstanceIdList(ctx context.Context, accountID string) ([]string, error){
	totalInstanceIdList := make([]string, 0)

	// 1、查询监控实例列表
	selector := fmt.Sprintf("%s=%s", cpromv1.BCEAccountIDLabel, accountID)

	labelSelector, err := labels.Parse(selector)
	if err != nil {
		logger.Errorf(ctx, fmt.Sprintf("parse labels: %v err: %v", selector, err))
		return totalInstanceIdList, err
	}

	var mm cpromv1.MonitorInstanceList

	if err = h.k8sClient(true).List(ctx, &mm, &client.ListOptions{
		LabelSelector: labelSelector,
	}); err != nil {
		logger.Errorf(ctx, fmt.Sprintf("list %s err: %v", selector, err))
		return totalInstanceIdList, err
	}

	for _, item := range mm.Items {
		totalInstanceIdList = append(totalInstanceIdList, item.Name)
	}

	return totalInstanceIdList, nil
}

*/

// HideFields4AR 为Alerting Rule隐藏不需要暴露给用户的字段
func HideFields4AR(ar *vmv1beta1.VMRule) {

	ar.Kind = ""
	ar.APIVersion = ""
	ar.SelfLink = ""
	ar.UID = ""
	ar.ResourceVersion = ""
	ar.Generation = 0
	ar.Finalizers = nil
	ar.ManagedFields = nil
	deleteBuiltInLabelKeys(ar)
	deleteBuiltInAnnotationKeys(ar)
}

func validateRule(rule vmv1beta1.Rule) (bool, string) {
	var err error
	var errMsg string

	for tagKey, tagValue := range rule.Labels { // labels中限制配置{{$value}}的tagValue
		// 1、去重tagValue中的空格符
		processedTagValue := strings.Replace(tagValue, " ", "", -1)
		if processedTagValue == "{{$value}}" {
			errMsg = fmt.Sprintf("标签(Labels)配置不合法，不支持配置\\{\\{\\$value\\}\\}，请检查. labelItem: %v=%v", tagKey, tagValue)
			return false, errMsg
		}
	}

	if _, err = metricsql.Parse(rule.Expr.String()); err != nil {
		errMsg = fmt.Sprintf("告警规则(PromQL)语法错误，请检查. PromQL: %v, err: %v", rule.Expr.String(), err.Error())
		return false, errMsg
	}

	// 校验告警内容
	tmpl := rule.Annotations["description"]
	for k, v := range templateMap {
		tmpl = strings.ReplaceAll(tmpl, k, v)
	}
	if _, err := template.New("example").Parse(tmpl); err != nil {
		errMsg = fmt.Sprintf("告警内容有语法错误，请检查. err: %v", err.Error())
		return false, errMsg
	}

	return true, ""
}

// RegisterAPI 注册API路由
func (h *AlertingRuleAPI) RegisterAPI(r *gin.Engine) {

	api := r.Group("/api/v1/cprom/alerting_rules")
	api.Use(handler.RequiredWithAuth(h.middlew))
	processAccountID := handler.ProcessAccountID() // 针对后端的请求特殊处理accountID，用param中的accountID替换ak、sk解析的accountID
	validateInstance := handler.ValidateInstance(h.k8sClient(true))
	//whitelistAccess := handler.WhitelistAccess(h.usersettingCli, h.config.Region, false)

	validate := handler.ValidateAlertingRule(h.k8sClient(true), cpromv1.MonitorAlertNamespace)

	api.POST("", validateInstance, h.Create)
	api.GET("", validateInstance, handler.Pagination(), h.List)

	api.GET("/:ID/templates", validateInstance, h.AlertTemplate)

	api.GET("/:ID", validateInstance, validate, h.Get)
	api.PUT("/:ID", validateInstance, validate, h.Update)
	api.POST("/validate", h.Validate)
	api.DELETE("/:ID", validateInstance, validate, h.Delete)

	// 提供给后端查询的接口
	api.GET("/:ID/backend", processAccountID, validateInstance, validate, h.GetForBackend)

	// 查询全量告警规则名的接口
	//apiTotal := r.Group("/api/v1/cprom/total_alerting_rule_names")
	//apiTotal.Use(handler.RequiredWithAuth(h.middlew))
	//apiTotal.GET("", h.ListTotal)
	// OpenApi V2版本
	apiOpenAPIV2 := r.Group("/v2")
	apiOpenAPIV2.Use(handler.RequiredWithAuth(h.middlew))
	validateInstance2 := handler.ValidateInstanceV2(h.k8sClient(true))
	validateAlert2 := handler.ValidateAlertingRuleV2(h.k8sClient(true), cpromv1.MonitorAlertNamespace)
	apiOpenAPIV2.POST("/alerting_rule", validateInstance2, h.CreateV2)
	apiOpenAPIV2.GET("/alerting_rule", validateInstance2, handler.Pagination(), h.ListV2)
	apiOpenAPIV2.GET("/alerting_rule_template", h.AlertTemplateV2)
	apiOpenAPIV2.PUT("/alerting_rule/:ID", validateInstance2, validateAlert2, h.UpdateV2)
	apiOpenAPIV2.GET("/alerting_rule/:ID", validateInstance2, validateAlert2, h.GetV2)
	apiOpenAPIV2.DELETE("/alerting_rule/:ID", validateInstance2, validateAlert2, h.DeleteV2)
	apiOpenAPIV2.PUT("/alerting_rule/:ID/batch_update", validateInstance2, h.BatchUpdate)

	// OpenApi 告警模板接口
	apiOpenAPIV2.GET("/alert_template", handler.Pagination(), h.ListTemplate)
	apiOpenAPIV2.POST("/alert_template", h.CreateTemplate)
	apiOpenAPIV2.GET("/alert_template/:TplID", h.GetTemplate)
	apiOpenAPIV2.PUT("/alert_template/:TplID", h.UpdateTemplate)
	apiOpenAPIV2.POST("/alert_template/batch_delete", h.DeleteTemplate)
	apiOpenAPIV2.PUT("/alert_template/:TplID/bind_instance", h.ApplyTemplate)
	apiOpenAPIV2.GET("/alert_template/:TplID/bind_instance", h.QueryTemplateBindings)
	apiOpenAPIV2.POST("/alert_template/export", h.BatchExportTemplate)
	apiOpenAPIV2.POST("/alert_template/import", h.BatchImportTemplate)
}
