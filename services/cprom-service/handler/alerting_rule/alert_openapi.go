package alerting_rule

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"regexp"
	"strconv"
	"strings"
	"time"

	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/retrypolicy"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/configuration"

	"github.com/gin-gonic/gin"
	"icode.baidu.com/baidu/cprom/cloud-stack/cprom-common/utils"
	cpromv1 "icode.baidu.com/baidu/cprom/cloud-stack/pkg/apis/cprom/v1"
	vmv1beta1 "icode.baidu.com/baidu/cprom/cloud-stack/pkg/apis/vm/v1beta1"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/cprom"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/errorcodev2"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/logger"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/models"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/mysql/alert_template"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/mysql/alerting_rule"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/mysql/model"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/mysql/vm_manage"
	"icode.baidu.com/baidu/cprom/cloud-stack/services/cprom-service/handler"
	"k8s.io/apimachinery/pkg/util/intstr"
	//"github.com/VictoriaMetrics/VictoriaMetrics/app/vmalert/templates"
	//victoriametricsv1beta1 "github.com/VictoriaMetrics/operator/api/v1beta1"
	//v1 "k8s.io/api/core/v1"
)

var (
	TplNameRegex = regexp.MustCompile(`^[\p{Han}a-zA-Z][\p{Han}a-zA-Z0-9_-]{2,49}$`)
	MessageRegex = regexp.MustCompile(`^[\p{Han}a-zA-Z0-9_-]{1,100}$`)
)

type CreateAlertRuleRequest struct {
	AlertName string `json:"alertName"`
	// Expr is query, that will be evaluated at dataSource
	// +optional
	Expr intstr.IntOrString `json:"expr"`
	// For evaluation interval in time.Duration format
	// 30s, 1m, 1h  or nanoseconds
	// +optional
	For             string `json:"for"`
	Description     string `json:"description"`
	Severity        string `json:"severity"`
	NotifyRuleID    string `json:"notifyRuleId"`
	AlertTemplateID string `json:"alertTemplateId"`

	// Labels will be added to rule configuration
	// +optional
	Labels map[string]string `json:"labels"`
	// Annotations will be added to rule configuration
	// +optional
	Annotations map[string]string `json:"annotations,omitempty"`
	Enable      bool              `json:"enable"`
}

type RuleDetail struct {
	AlertRuleID   string             `json:"alertId"`
	AlertRuleName string             `json:"alertName"`
	Expr          intstr.IntOrString `json:"expr"`
	For           string             `json:"for"`
	Description   string             `json:"description"`
	Enable        bool               `json:"enable,omitempty"`
	Severity      string             `json:"severity"`
	NotifyRuleID  string             `json:"notifyRuleId"`
	Labels        map[string]string  `json:"labels"`
	Annotations   map[string]string  `json:"annotations,omitempty"`
}

type AlertTemplateConfig struct {
	RuleTemplates []RuleTemplate `json:"ruleTemplates"`
}

type RuleTemplate struct {
	AlertName   string `json:"alertName"`
	Expr        string `json:"expr"`
	For         string `json:"for"`
	Description string `json:"description"`
}
type Annotation struct {
	Description string `yaml:"description"`
}

type Template struct {
	AlertTemplateID   string             `json:"alertTemplateId"`
	AlertTemplateName string             `json:"alertTemplateName"`
	Expr              intstr.IntOrString `json:"expr"`
	For               string             `json:"for"`
	Description       string             `json:"description"`
	Message           string             `json:"message"`
	Type              string             `json:"type"`
	Version           string             `json:"version"`
	Level             string             `json:"level"`
	CreateTime        time.Time          `json:"createTime"`
	UpdateTime        time.Time          `json:"updateTime"`
	Labels            map[string]string  `json:"labels,omitempty" protobuf:"bytes,11,rep,name=labels"`
	Annotations       map[string]string  `json:"annotations,omitempty" protobuf:"bytes,12,rep,name=annotations"`
	BoundInstances    []string           `json:"boundInstances"`
}

type CreateTemplateRequest struct {
	AlertTemplateName string `json:"alertTemplateName"`
	// Expr is query, that will be evaluated at dataSource
	// +optional
	Expr intstr.IntOrString `json:"expr"`
	// For evaluation interval in time.Duration format
	// 30s, 1m, 1h  or nanoseconds
	// +optional
	For          string `json:"for"`
	Description  string `json:"description"`
	Message      string `json:"message"`
	Level        string `json:"level"`
	NotifyRuleID string `json:"notifyRuleId"`
	// Labels will be added to rule configuration
	// +optional
	Labels map[string]string `json:"labels"`
	// Annotations will be added to rule configuration
	// +optional
	Annotations map[string]string `json:"annotations,omitempty"`
}

type TemplateDetail struct {
	AlertTemplateName string             `json:"alertTemplateName"`
	Expr              intstr.IntOrString `json:"expr"`
	For               string             `json:"for"`
	Description       string             `json:"description"`
	Message           string             `json:"message"`
	Level             string             `json:"level"`
	CreateTime        time.Time          `json:"createTime"`
	UpdateTime        time.Time          `json:"updateTime"`
	NotifyRuleID      string             `json:"notifyRuleId"`
	Version           string             `json:"version"`
	Labels            map[string]string  `json:"labels"`
	Annotations       map[string]string  `json:"annotations,omitempty"`
}

type AlertRuleIDs struct {
	AlertingRuleIds []string `json:"alertingRuleIds"`
}

type DeleteTemplateRequest struct {
	AlertTemplateIds []string `json:"alertTemplateIds"`
}

type ApplyTemplateRequest struct {
	InstanceID string `json:"instanceId"`
	Region     string `json:"region"`
}

type QueryBindingRequest struct {
	Region string `json:"region"`
}

type BindingStatus struct {
	BindingStatus []BindingDetail `json:"bindingStatus"`
}

type BindingDetail struct {
	InstanceName   string `json:"instanceName"`
	InstanceID     string `json:"instanceId"`
	SyncTime       string `json:"syncTime"`
	CurrentVersion string `json:"currentVersion"`
	NeedUpdate     bool   `json:"needUpdate"`
}

type ExportRequest struct {
	AlertTemplateIds []string `json:"alertTemplateIds"`
}

type ImportRequest struct {
	Groups   []vmv1beta1.RuleGroup `json:"groups"`
	Override bool                  `json:"override"`
}

type ImportResponse struct {
	AlertTemplateIds []string `json:"alertTemplateIds"`
}

type BatchRequest struct {
	AlertingRuleIds []string `json:"alertingRuleIds"`
}

// OpenApi V2版本开放接口，继承原有create接口实现
func (h *AlertingRuleAPI) CreateV2(c *gin.Context) {
	var (
		ctx        = handler.NewContext(c)
		res        = errorcodev2.NewResponse()
		accountID  = c.MustGet("accountID").(string)
		instanceID = c.MustGet("instanceId").(string)
		//namespace  = instanceID
		mi = c.MustGet("instance").(*cpromv1.MonitorInstance)
	)
	requestID := logger.GetRequestID(ctx)
	instanceName := mi.Spec.InstanceName
	var req CreateAlertRuleRequest

	if err := c.Bind(&req); err != nil {
		err := fmt.Errorf("bind post data err: %v", err)
		logger.Errorf(ctx, "%v", err)

		res.Message = err.Error()
		c.JSON(http.StatusBadRequest, errorcodev2.BadRequestWithMessage(requestID, res.Message))
		return
	}

	m := vmv1beta1.VMRule{}
	m.Spec.Groups = make([]vmv1beta1.RuleGroup, 1)
	m.Spec.Groups[0].Rules = make([]vmv1beta1.Rule, 1)
	m.Spec.Groups[0].Rules[0].Labels = make(map[string]string)
	m.Spec.Groups[0].Rules[0].Annotations = make(map[string]string)
	m.Spec.Groups[0].Name = req.AlertName
	m.Spec.Groups[0].Rules[0].Alert = req.AlertName
	m.Spec.Groups[0].Rules[0].Expr = req.Expr
	m.Spec.Groups[0].Rules[0].For = req.For
	if len(req.Annotations) > 0 {
		m.Spec.Groups[0].Rules[0].Annotations = req.Annotations
	}
	m.Spec.Groups[0].Rules[0].Annotations[cpromv1.DescriptionAnnotation] = req.Description

	if len(req.Labels) > 0 {
		m.Spec.Groups[0].Rules[0].Labels = req.Labels
	}
	m.Spec.Groups[0].Rules[0].Labels[cpromv1.NotifyRuleIDLabel] = req.NotifyRuleID
	m.Spec.Groups[0].Rules[0].Labels["cprom_severity"] = req.Severity

	// 从cookie里获取用户名
	userName, err := c.Cookie("bce-login-display-name")
	totalCookie, err := c.Cookie("") // 获取所有cookie
	if err != nil {
		logger.Errorf(ctx, "[CreateAlertingRuleHandler] get userName from Cookie failed. totalCookie=[%v], errorInfo=[%v]", totalCookie, err.Error())
		// 兼容混合云调用接口没有传cookie的情况，不直接报错返回，指定userName为admin
		userName = "admin"
	}

	if m.Labels == nil {
		m.Labels = map[string]string{}
	}

	m.Labels[cpromv1.BCEAccountIDLabel] = accountID
	m.Labels[cpromv1.InstanceIDLabel] = instanceID // todo 0822 多租户中这里应该删掉，用accountId、projectId替代（因为迁移会导致instanceId变化）
	m.Labels[cpromv1.EnableLabel] = "true"
	m.Labels[cpromv1.VMRuleTypeLabel] = "alerting"

	alertingRuleID, err := utils.GenerateID(ctx, h.k8sClient(false),
		alertingRulePrefix, h.config.ResourceClusterRegionID,
		cpromv1.MonitorAlertNamespace, &vmv1beta1.VMRule{}, // 在monitor_alert所在的ns(cprom-alert)下生成唯一的alertingRuleId
	)
	if err != nil {
		res.Message = fmt.Sprintf("genAlertingRuleID err: %v", err)
		logger.Errorf(ctx, "%v", res.Message)

		c.JSON(http.StatusInternalServerError, errorcodev2.InternalServerErrorWithMessage(requestID, res.Message))
		return
	}
	m.Labels[cpromv1.AlertingRuleIDLabel] = alertingRuleID
	m.Name = alertingRuleID
	m.Namespace = cpromv1.MonitorAlertNamespace

	alertingRuleName := ""
	notifyRuleId := ""

	for _, group := range m.Spec.Groups {
		alertingRuleName = group.Name
		for _, rule := range group.Rules {

			// 校验rule是否是alerting rule
			if rule.Alert == "" {
				res.Message = fmt.Sprintf("input rule is not a valide alerting rule, rule:%v", rule)
				logger.Errorf(ctx, "%v", res.Message)
				c.JSON(http.StatusBadRequest, errorcodev2.BadRequestWithMessage(requestID, res.Message))
				return
			}

			//var notifyRuleNamespace = types.NamespacedName{
			//	// 一个子元素Rule关联一个notify rule
			//	Name:      rule.Labels[cpromv1.NotifyRuleIDLabel],
			//	Namespace: instanceID,
			//}

			notifyRuleId = rule.Labels[cpromv1.NotifyRuleIDLabel]
			if !h.validateNotifyRule(ctx, accountID, notifyRuleId) {
				res.Message = fmt.Sprintf("validate related notifyRule failed. "+
					"accountId=[%v], notifyRuleId=[%v]", accountID, notifyRuleId)
				logger.Errorf(ctx, "%v", res.Message)
				c.JSON(http.StatusBadRequest, errorcodev2.BadRequestWithMessage(requestID, res.Message))
				return
			}
			// add alert id label。
			// 放在alert中的内置标签，用于event-keeper查询告警规则使用
			rule.Labels[cpromv1.AccountIdLabel] = accountID
			rule.Labels[cpromv1.MonitorInstanceIdLabel] = instanceID
			rule.Labels[cpromv1.AlertSourceIDLabel] = alertingRuleID
			rule.Annotations[cpromv1.CalculateValueAnnotation] = "{{ $value }}" // 内置注解，用于记录promQL计算结果

			// 校验rule的expr、labels、annotations
			isValidate, errMsg := validateRule(rule)
			if !isValidate {
				res.Message = errMsg
				logger.Errorf(ctx, "%v", res.Message)
				c.JSON(http.StatusBadRequest, errorcodev2.BadRequestWithMessage(requestID, res.Message))
				return
			}
		}
	}

	// 0. 限制recording rule不得超过400个
	count, err := alerting_rule.CountById(instanceID)
	limit := 400
	if h.config.AlertRuleLimit > 0 {
		limit = h.config.AlertRuleLimit
	}
	if count > limit {
		res.Message = fmt.Sprintf("alert rule can not be larger than 400. isntanceId:%v err: %v", instanceID, err)
		logger.Errorf(ctx, "%v", res.Message)
		c.JSON(http.StatusTooManyRequests, errorcodev2.StatusTooManyRequestsErrorWithMessage(requestID, res.Message))
		return
	}

	// 1、持久化存储：将vmRule结构体转换为json字符串存入mysql
	vmRuleBytes, err := json.Marshal(&m) // 注意，此时m.Namespace为instanceID。目前针对双写情况ns=cprom-alert
	if err != nil {
		res.Message = fmt.Sprintf("json.Marshal alertingRule failed. namespace:%v, alertingRuleName:%v err: %v", m.Namespace, m.Name, err)
		logger.Errorf(ctx, "%v", res.Message)
		c.JSON(http.StatusBadRequest, errorcodev2.BadRequestWithMessage(requestID, res.Message))
		return
	}
	alertingRuleRecord := model.AlertingRule{
		BCEAccountID:       accountID,
		NewInstanceID:      instanceID,
		AlertingRuleID:     alertingRuleID,
		AlertingRuleName:   alertingRuleName,
		AlertingRuleDetail: string(vmRuleBytes), // 此时的vmRuleBytes中不包含Create后m.Metadata中新填充的resourceVersion、selfLink等字段，以后也不会包含
		NotifyRuleID:       notifyRuleId,
		Operator:           userName,
	}
	err = alerting_rule.Insert(alertingRuleRecord)
	if err != nil {
		logger.Errorf(ctx, "[InsertAlertingRule] to DB failed. errorInfo=[%v]", err.Error())
		res.Message = fmt.Sprintf("insert record to DB failed.")
		c.JSON(http.StatusInternalServerError, errorcodev2.InternalServerErrorWithMessage(requestID, res.Message))
		return
	}

	// copy一份m，否则第一个ns下执行完Create后，m.Metadata中会被填充resourceVersion、selfLink等字段，无法在第二个ns下create
	mCopy := *(m.DeepCopy())

	// todo： this logic will remove finally
	manageAS := vm_manage.TAccountManage{}
	err = manageAS.Get(nil, instanceID)
	if err != nil {
		logger.Errorf(ctx, "[TAccountManage] to DB failed. errorInfo=[%v]", err.Error())
		res.Message = fmt.Sprintf("get data from manage acount DB failed.")
		c.JSON(http.StatusInternalServerError, errorcodev2.InternalServerErrorWithMessage(requestID, res.Message))
		return
	}

	/*
		if len(manageAS.FNewInstanceId) < 1 {
			// origin instance
			m.Namespace = instanceID
			if err = h.k8sClient(false).Create(ctx, &m); err != nil {  // 执行完Create后，m.Metadata中会被填充resourceVersion、selfLink等字段，
				// 如果创建失败，则对应删除mysql中的记录
				res.Message = fmt.Sprintf("create AlertingRule: %v/%v err: %v", m.Namespace, m.Name, err)
				logger.Errorf(ctx, "%v", res.Message)
				c.JSON(http.StatusBadRequest, res)
				return
			}
			logger.Infof(ctx, "create AlertingRule: %v/%v successful", m.Namespace, m.Name)
		}
	*/

	// 2.2 创建cprom-alert下的alertingRule
	mCopy.Namespace = cpromv1.MonitorAlertNamespace
	if err = h.k8sClient(false).Create(ctx, &mCopy); err != nil {
		res.Message = fmt.Sprintf("create AlertingRule: %v/%v err: %v", mCopy.Namespace, mCopy.Name, err)
		logger.Errorf(ctx, "%v", res.Message)
		c.JSON(http.StatusBadRequest, errorcodev2.BadRequestWithMessage(requestID, res.Message))
		return
	}
	logger.Infof(ctx, "create AlertingRule: %v/%v successful", mCopy.Namespace, mCopy.Name)

	if req.AlertTemplateID != "" {
		alertTemplate, err := alert_template.QueryById(req.AlertTemplateID, accountID)
		if err != nil {
			res.Message = fmt.Sprintf("not found alertingRuleTpl. "+
				"accountID=[%v], alertingRuleTpl=[%v]", accountID, req.AlertTemplateID)
			logger.Errorf(ctx, res.Message)
			c.JSON(http.StatusNotFound, res)
			c.Abort()
			return
		}

		alertTplMapping := model.TemplateInstanceMapping{
			BCEAccountID:    accountID,
			AlertTemplateID: req.AlertTemplateID,
			InstanceID:      instanceID,
			InstanceName:    *instanceName,
			AlertRuleID:     alertingRuleID, // 此时的vmRuleBytes中不包含Create后m.Metadata中新填充的resourceVersion、selfLink等字段，以后也不会包含
			Version:         alertTemplate.Version,
			Region:          h.config.Region,
			Operator:        "admin",
		}
		err = alert_template.InsertMapping(alertTplMapping)
		if err != nil {
			res.Message = fmt.Sprintf("[Insert alertTplMapping] to DB failed."+
				"accountID=[%v], alertingRuleTpl=[%v]", accountID, req.AlertTemplateID)
			logger.Errorf(ctx, res.Message)
		}
	}

	result := gin.H{"alertId": m.Name}
	c.JSON(http.StatusOK, result)

}

// OpenApi V2版本开放接口，继承原有实现
func (h *AlertingRuleAPI) AlertTemplateV2(c *gin.Context) {
	var (
		cfg = h.config
	)

	var tempRes []RuleTemplate
	listTemplates := cfg.AlertTemplate()
	for _, template := range listTemplates.Rules {
		tempRes = append(tempRes, RuleTemplate{
			AlertName:   template.Alert,
			Expr:        template.Expr,
			For:         template.For,
			Description: template.Annotations.Description,
		})
	}
	res := AlertTemplateConfig{
		RuleTemplates: tempRes,
	}
	c.JSON(http.StatusOK, res)
}

func (h *AlertingRuleAPI) BatchUpdate(c *gin.Context) {
	var (
		ctx        = handler.NewContext(c)
		res        = errorcodev2.NewResponse()
		accountID  = c.MustGet("accountID").(string)
		instanceID = c.MustGet("instanceId").(string)
		action     = c.Query("action")
	)
	requestID := logger.GetRequestID(ctx)

	var req DeleteTemplateRequest
	if err := c.Bind(&req); err != nil {
		err := fmt.Errorf("bind post data err: %v", err)
		logger.Errorf(ctx, "%v", err)

		res.Message = err.Error()
		c.JSON(http.StatusBadRequest, errorcodev2.BadRequestWithMessage(requestID, res.Message))
		return
	}

}

// OpenApi V2版本开放接口，继承原有接口实现
func (h *AlertingRuleAPI) UpdateV2(c *gin.Context) {
	var (
		ctx                = handler.NewContext(c)
		res                = errorcodev2.NewResponse()
		accountID          = c.MustGet("accountID").(string)
		instanceID         = c.MustGet("instanceId").(string)
		alertingRuleID     = c.MustGet("alertingRuleID").(string)
		alertingRuleRecord = c.MustGet("alertingRuleRecord").(*model.AlertingRule)
		//alertingRuleInstance, exist   	= c.Get("alertingRuleInstance")  // ns=监控实例Id查询的vmRule，其中包含了Metadata.resourceVersion、selfLink等k8s自动生成的字段
		alertingRule = c.MustGet("alertingRule").(*vmv1beta1.VMRule) // ns=cprom-alert查询的vmRule，其中包含了Metadata.resourceVersion、selfLink等k8s自动生成的字段
		mu           = vmv1beta1.VMRule{}
	)

	requestID := logger.GetRequestID(ctx)
	var req CreateAlertRuleRequest
	if err := c.Bind(&req); err != nil {
		res.Message = fmt.Sprintf("bind post data err: %v", err)
		logger.Errorf(ctx, "%v", res.Message)

		c.JSON(http.StatusBadRequest, errorcodev2.BadRequestWithMessage(requestID, res.Message))
		return
	}

	mu.Spec.Groups = make([]vmv1beta1.RuleGroup, 1)
	mu.Spec.Groups[0].Rules = make([]vmv1beta1.Rule, 1)
	mu.Spec.Groups[0].Rules[0].Labels = make(map[string]string)      // 初始化 Labels map
	mu.Spec.Groups[0].Rules[0].Annotations = make(map[string]string) // 初始化 Annotations map
	mu.Spec.Groups[0].Name = req.AlertName
	mu.Spec.Groups[0].Rules[0].Alert = req.AlertName
	mu.Spec.Groups[0].Rules[0].Expr = req.Expr
	mu.Spec.Groups[0].Rules[0].For = req.For
	if len(req.Annotations) > 0 {
		mu.Spec.Groups[0].Rules[0].Annotations = req.Annotations
	}
	mu.Spec.Groups[0].Rules[0].Annotations[cpromv1.DescriptionAnnotation] = req.Description

	if len(req.Labels) > 0 {
		mu.Spec.Groups[0].Rules[0].Labels = req.Labels
	}
	mu.Spec.Groups[0].Rules[0].Labels[cpromv1.NotifyRuleIDLabel] = req.NotifyRuleID
	mu.Spec.Groups[0].Rules[0].Labels["cprom_severity"] = req.Severity
	mu.Labels = make(map[string]string)
	mu.Labels[cpromv1.EnableLabel] = strconv.FormatBool(req.Enable)

	// 从cookie里获取用户名
	userName, err := c.Cookie("bce-login-display-name")
	totalCookie, err := c.Cookie("") // 获取所有cookie
	if err != nil {
		logger.Errorf(ctx, "[UpdateAlertingRuleHandler] get userName from Cookie failed. totalCookie=[%v], errorInfo=[%v]", totalCookie, err.Error())
		// 兼容混合云调用接口没有传cookie的情况，不直接报错返回，指定userName为admin
		userName = "admin"
	}

	// 0、将从mysql中查询的记录转化为k8s的结构，
	m := vmv1beta1.VMRule{} // alertingRule的旧记录
	err = json.Unmarshal([]byte(alertingRuleRecord.AlertingRuleDetail), &m)
	if err != nil {
		logger.Errorf(ctx, "json.unmarshal for alertingRuleRecord.AlertingRuleDetail failed. errorInfo=[%v]", err.Error())
		res.Message = fmt.Sprintf("update alertingRule failed. erroInfo=[json.unmarshal record from DB failed]")
		c.JSON(http.StatusInternalServerError, errorcodev2.InternalServerErrorWithMessage(requestID, res.Message))
		return
	}

	// 用待更新的alertingRule配置替换旧记录的部分字段
	m.Spec = mu.Spec

	// 更新Enable Label
	if v, ok := mu.Labels[cpromv1.EnableLabel]; ok {
		m.Labels[cpromv1.EnableLabel] = v
		//alertingRuleInstance.Labels[cpromv1.EnableLabel] = v
		alertingRule.Labels[cpromv1.EnableLabel] = v
	}

	alertingRuleName := ""
	notifyRuleId := ""

	// update时也要与create保持一致，默认增加内置标签指定alerting_rule_id
	// 对update的alertingRule也增加通知策略的校验 -> 与create保持一致
	for _, group := range m.Spec.Groups {
		alertingRuleName = group.Name
		for _, rule := range group.Rules {
			// 校验rule是否是alerting rule
			if rule.Alert == "" {
				res.Message = fmt.Sprintf("update rule is not a valide alerting rule, rule:%v", rule)
				c.JSON(http.StatusBadRequest, errorcodev2.BadRequestWithMessage(requestID, res.Message))
				return
			}

			//var notifyRuleNamespace = types.NamespacedName{
			//	// 一个子元素Rule关联一个notify rule
			//	Name:      rule.Labels[cpromv1.NotifyRuleIDLabel],
			//	Namespace: instanceID,
			//}

			notifyRuleId = rule.Labels[cpromv1.NotifyRuleIDLabel]
			if !h.validateNotifyRule(ctx, accountID, notifyRuleId) {
				res.Message = fmt.Sprintf("validate related notifyRule failed. "+
					"accountId=[%v], notifyRuleId=[%v]", accountID, notifyRuleId)
				c.JSON(http.StatusBadRequest, errorcodev2.BadRequestWithMessage(requestID, res.Message))
				return
			}

			// 放在alert中的内置标签，用于event-keeper查询告警规则使用
			rule.Labels[cpromv1.AccountIdLabel] = accountID
			rule.Labels[cpromv1.MonitorInstanceIdLabel] = instanceID // todo 0822 多租户中这里应该删掉，用accountId、projectId替代（因为迁移会导致instanceId变化）
			rule.Labels[cpromv1.AlertSourceIDLabel] = alertingRuleID
			rule.Annotations[cpromv1.CalculateValueAnnotation] = "{{ $value }}" // 内置注解，用于记录promQL计算结果

			// 校验rule的expr、labels、annotations
			isValidate, errMsg := validateRule(rule)
			if !isValidate {
				res.Message = errMsg
				c.JSON(http.StatusBadRequest, errorcodev2.BadRequestWithMessage(requestID, res.Message))
				return
			}
		}
	}

	// 1、持久化存储：将vmRule结构体转换为json字符串存入mysql
	vmRuleBytes, err := json.Marshal(&m) // 注意，此时m.Namespace为instanceID。目前针对双写情况ns=cprom-alert
	if err != nil {
		res.Message = fmt.Sprintf("json.Marshal alertingRule failed. namespace:%v, alertingRuleName:%v err: %v", m.Namespace, m.Name, err)
		logger.Errorf(ctx, "%v", res.Message)
		c.JSON(http.StatusBadRequest, errorcodev2.BadRequestWithMessage(requestID, res.Message))
		return
	}
	newAlertingRuleRecord := model.AlertingRule{
		BCEAccountID:       accountID,
		NewInstanceID:      instanceID,
		AlertingRuleID:     alertingRuleID,
		AlertingRuleName:   alertingRuleName,
		AlertingRuleDetail: string(vmRuleBytes),
		NotifyRuleID:       notifyRuleId,
		Operator:           userName,
	}
	err = alerting_rule.Update(newAlertingRuleRecord)
	if err != nil {
		logger.Errorf(ctx, "[UpdateAlertingRule] to DB failed. errorInfo=[%v]", err.Error())
		res.Message = fmt.Sprintf("update record to DB failed.")
		c.JSON(http.StatusInternalServerError, errorcodev2.InternalServerErrorWithMessage(requestID, res.Message))
		return
	}
	logger.Infof(ctx, "update alertingRule in DB successful")

	/*
		if exist {
			alertingRuleInstance := alertingRuleInstance.(*vmv1beta1.VMRule)
			alertingRuleInstance.Spec = m.Spec
			if err = h.k8sClient(false).Update(ctx, alertingRuleInstance); err != nil {
				res.Message = fmt.Sprintf("update alertingRule failed. %v/%v err: %v", alertingRuleInstance.Namespace, alertingRuleInstance.Name, err)
				logger.Errorf(ctx, "%v", res.Message)
				c.JSON(http.StatusInternalServerError, res)
				return
			}
			logger.Infof(ctx, "update alertingRule: %v/%v successful", alertingRuleInstance.Namespace, alertingRuleInstance.Name)
		}

	*/

	// 2.2 更新cprom-alert下的alertingRule
	alertingRule.Spec = m.Spec
	if err = h.k8sClient(false).Update(ctx, alertingRule); err != nil {
		res.Message = fmt.Sprintf("update alertingRule failed. %v/%v err: %v", alertingRule.Namespace, alertingRule.Name, err)
		logger.Errorf(ctx, "%v", res.Message)
		c.JSON(http.StatusInternalServerError, errorcodev2.InternalServerErrorWithMessage(requestID, res.Message))
		return
	}
	logger.Infof(ctx, "update alertingRule: %v/%v successful", alertingRule.Namespace, alertingRule.Name)

	c.JSON(http.StatusOK, gin.H{})
	return
}

// OpenApi V2版本开放接口，继承原有LIST接口实现
func (h *AlertingRuleAPI) ListV2(c *gin.Context) {
	var (
		ctx = handler.NewContext(c)
		//accountID   = c.MustGet("accountID").(string)
		instanceID  = c.MustGet("instanceId").(string)
		res         = errorcodev2.NewResponse()
		pageNo      = c.GetInt("pageNo")
		pageSize    = c.GetInt("pageSize")
		keywordType = c.Query("keywordType") // keywordType 支持：alertName
		keyword     = c.Query("keyword")
	)

	requestID := logger.GetRequestID(ctx)
	// 1、根据pageSize、pageNo、instanceID从mysql查对应的告警规则列表
	alertingRuleName := ""
	if keywordType == "alertName" {
		alertingRuleName = keyword
	}
	pageSizeInt64 := int64(pageSize)
	pageNoInt64 := int64(pageNo)
	totalCount, alertingRuleRecords, err := alerting_rule.QueryLimitByPageSize(alertingRuleName, instanceID, pageSizeInt64, pageNoInt64)
	if err != nil {
		res.Message = fmt.Sprintf("batch query alertingRecords from DB failed.")
		logger.Errorf(ctx, "[QueryLimitByPageSize] from DB failed. errorInfo=%s", err.Error())
		c.JSON(http.StatusInternalServerError, errorcodev2.InternalServerErrorWithMessage(requestID, res.Message))
		return
	}

	items := make([]*vmv1beta1.VMRule, 0)
	for _, alertingRuleRecord := range alertingRuleRecords { // alertingRuleRecords已经按照updateTime降序排列过了
		// 1、将AlertingRuleDetail部分转化为结构体
		m := vmv1beta1.VMRule{}
		err = json.Unmarshal([]byte(alertingRuleRecord.AlertingRuleDetail), &m)
		if err != nil {
			logger.Errorf(ctx, "JsonUnmarshal for alertingRuleRecord.AlertingRuleDetail failed. "+
				"aleringRuleId=[%v], errorInfo=[%v]", alertingRuleRecord.AlertingRuleID, err.Error())
			continue
		}

		// 2、依次处理每个alertingRule
		// 2.1 隐藏内置字段
		HideFields4AR(&m)

		// 2.2 兼容老数据，对老策略labels里没有SeverityLabel字段的设置默认值为notice
		severity, ok := m.Spec.Groups[0].Rules[0].Labels[cpromv1.SeverityLabel]
		if !ok || severity == "" { // 老策略中没有SeverityLabel这个值或为空，给一个内置值
			m.Spec.Groups[0].Rules[0].Labels[cpromv1.SeverityLabel] = "notice"
		}

		items = append(items, &m)
	}

	rulesDetails := h.ConvertListToDto(items)

	listResp := models.ListResponse{
		KeywordType: keywordType,
		Keyword:     keyword,
		PageNo:      pageNo,
		PageSize:    pageSize,
		TotalCount:  totalCount,
		Items:       rulesDetails,
	}

	c.JSON(http.StatusOK, listResp)
}

// OpenApi V2版本开放接口，继承原有接口实现
func (h *AlertingRuleAPI) GetV2(c *gin.Context) {
	var (
		ctx = handler.NewContext(c)
		res = errorcodev2.NewResponse()
		//instanceID     = c.MustGet("instanceID").(string)
		alertingRuleID     = c.MustGet("alertingRuleID").(string)
		alertingRuleRecord = c.MustGet("alertingRuleRecord").(*model.AlertingRule)
	)

	requestID := logger.GetRequestID(ctx)
	logger.Infof(ctx, "get alertingRuleID=[%v], alertingRule=[%v]", alertingRuleID, alertingRuleRecord)

	// 1、将AlertingRuleDetail部分转化为结构体
	m := vmv1beta1.VMRule{}
	err := json.Unmarshal([]byte(alertingRuleRecord.AlertingRuleDetail), &m)
	if err != nil {
		logger.Errorf(ctx, "JsonUnmarshal for alertingRuleRecord.AlertingRuleDetail failed. errorInfo=[%v]", err.Error())
		res.Message = fmt.Sprintf("get record from DB failed.")
		c.JSON(http.StatusInternalServerError, errorcodev2.InternalServerErrorWithMessage(requestID, res.Message))
		return
	}

	// 2、按照原有处理方式处理m结构
	HideFields4AR(&m)

	severity, ok := m.Spec.Groups[0].Rules[0].Labels[cpromv1.SeverityLabel]
	if !ok || severity == "" { // 老策略中没有SeverityLabel这个值或为空，给一个内置值
		m.Spec.Groups[0].Rules[0].Labels[cpromv1.SeverityLabel] = "notice"
	}
	enable, _ := strconv.ParseBool(m.Labels[cpromv1.EnableLabel])
	notifyRuleID, ok := m.Spec.Groups[0].Rules[0].Labels["cprom_notify_rule_id"]
	if ok && notifyRuleID == "notify-default" {
		m.Spec.Groups[0].Rules[0].Labels["cprom_notify_rule_id"] = ""
	}
	ruleDetail := RuleDetail{
		AlertRuleID:   m.Name,
		AlertRuleName: m.Spec.Groups[0].Rules[0].Alert,
		Expr:          m.Spec.Groups[0].Rules[0].Expr,
		For:           m.Spec.Groups[0].Rules[0].For,
		Description:   m.Spec.Groups[0].Rules[0].Annotations[cpromv1.DescriptionAnnotation],
		Enable:        enable,
		Severity:      m.Spec.Groups[0].Rules[0].Labels[cpromv1.SeverityLabel],
		NotifyRuleID:  m.Spec.Groups[0].Rules[0].Labels[cpromv1.NotifyRuleIDLabel],
		Labels:        m.Spec.Groups[0].Rules[0].Labels,
		Annotations:   m.Spec.Groups[0].Rules[0].Annotations,
	}
	delete(ruleDetail.Labels, cpromv1.NotifyRuleIDLabel)

	c.JSON(http.StatusOK, ruleDetail)
	return
}

// OpenApi V2版本开放接口，继承原有接口实现
func (h *AlertingRuleAPI) DeleteV2(c *gin.Context) {
	var (
		ctx            = handler.NewContext(c)
		res            = errorcodev2.NewResponse()
		instanceID     = c.MustGet("instanceId").(string)
		alertingRuleID = c.MustGet("alertingRuleID").(string)
		//alertingRuleInstance, exist = c.Get("alertingRuleInstance")  // ns=监控实例Id查询的vmRule
		alertingRule = c.MustGet("alertingRule").(*vmv1beta1.VMRule) // ns=cprom-alert查询的vmRule
		accountID    = c.MustGet("accountID").(string)
	)

	/*
		if exist {
			alertingRuleInstance := alertingRuleInstance.(*vmv1beta1.VMRule)
			if err := h.k8sClient(false).Delete(ctx, alertingRuleInstance); err != nil {
				res.Message = fmt.Sprintf("delete alertingRule failed. %v/%v err: %v", alertingRuleInstance.Namespace, alertingRuleInstance.Name, err)
				logger.Errorf(ctx, "%v", res.Message)
				c.JSON(http.StatusInternalServerError, res)
				return
			}
			logger.Infof(ctx, "delete alertingRule: %v/%v successful", alertingRuleInstance.Namespace, alertingRuleInstance.Name)
		}

	*/

	requestID := logger.GetRequestID(ctx)

	// 1.2 删除cprom-alert下的alertingRule
	if err := h.k8sClient(false).Delete(ctx, alertingRule); err != nil {
		res.Message = fmt.Sprintf("delete alertingRule failed. %v/%v err: %v", alertingRule.Namespace, alertingRule.Name, err)
		logger.Errorf(ctx, "%v", res.Message)
		c.JSON(http.StatusInternalServerError, errorcodev2.InternalServerErrorWithMessage(requestID, res.Message))
		return
	}
	logger.Infof(ctx, "delete alertingRule: %v/%v successful", alertingRule.Namespace, alertingRule.Name)

	// 2、删除alertingRuleId在alertingRule表中对应的记录
	err := alerting_rule.Delete(alertingRuleID, instanceID)
	if err != nil {
		logger.Errorf(ctx, "[DeleteAlertingRule] to DB failed. errorInfo=[%v]", err.Error())
		res.Message = fmt.Sprintf("delete record to DB failed.")
		c.JSON(http.StatusInternalServerError, errorcodev2.InternalServerErrorWithMessage(requestID, res.Message))
		return
	}

	err = alert_template.DeleteMappingByAlertIDAndAccountID(alertingRuleID, accountID)
	if err != nil {
		logger.Errorf(ctx, "[DeleteAlertingRuleMapping] to DB failed. errorInfo=[%v]", err.Error())
	}
	logger.Infof(ctx, "delete alertingRule in DB successful")

	c.JSON(http.StatusOK, gin.H{})
	return
}

// 转为前端展示结构
func (h *AlertingRuleAPI) ConvertListToDto(vmRules []*vmv1beta1.VMRule) []RuleDetail {
	var listRules []RuleDetail
	for _, vmRule := range vmRules {
		notifyRuleID, ok := vmRule.Spec.Groups[0].Rules[0].Labels["cprom_notify_rule_id"]
		if ok && notifyRuleID == "notify-default" {
			vmRule.Spec.Groups[0].Rules[0].Labels["cprom_notify_rule_id"] = ""
		}
		enable, _ := strconv.ParseBool(vmRule.Labels[cpromv1.EnableLabel])
		ruleDetail := RuleDetail{
			AlertRuleID:   vmRule.Name,
			AlertRuleName: vmRule.Spec.Groups[0].Rules[0].Alert,
			Expr:          vmRule.Spec.Groups[0].Rules[0].Expr,
			For:           vmRule.Spec.Groups[0].Rules[0].For,
			Severity:      vmRule.Spec.Groups[0].Rules[0].Labels[cpromv1.SeverityLabel],
			Description:   vmRule.Spec.Groups[0].Rules[0].Annotations[cpromv1.DescriptionAnnotation],
			Enable:        enable,
			NotifyRuleID:  vmRule.Spec.Groups[0].Rules[0].Labels[cpromv1.NotifyRuleIDLabel],
			Labels:        vmRule.Spec.Groups[0].Rules[0].Labels,
			Annotations:   vmRule.Spec.Groups[0].Rules[0].Annotations,
		}
		delete(ruleDetail.Labels, cpromv1.NotifyRuleIDLabel)
		listRules = append(listRules, ruleDetail)
	}
	return listRules
}

// ListTemplate 获取告警模板列表，支持系统模板和自定义模板。
func (h *AlertingRuleAPI) ListTemplate(c *gin.Context) {
	var (
		ctx             = handler.NewContext(c)
		accountId       = c.MustGet("accountID").(string)
		res             = errorcodev2.NewResponse()
		pageNo          = c.GetInt("pageNo")
		pageSize        = c.GetInt("pageSize")
		keywordType     = c.Query("keywordType")
		keyword         = c.Query("keyword")
		templateType    = c.Query("type")
		level           = c.Query("level")
		requestID       = logger.GetRequestID(ctx)
		sysTemplates    = make([]Template, 0)
		customTemplates = make([]Template, 0)
	)

	if pageSize <= 0 {
		pageSize = 10
	}

	if pageSize > 1000 {
		pageSize = 1000
	}
	if pageNo <= 0 {
		pageNo = 1
	}

	if templateType != "" {
		if templateType != "system" && templateType != "custom" { // 扩展其他类型
			res.Message = fmt.Sprintf("The templateType is invalid: %s, only supports system or custom.", templateType)
			c.JSON(http.StatusBadRequest, errorcodev2.BadRequestWithMessage(requestID, res.Message))
			return
		}
	}

	if level != "" {
		if level != "notice" && level != "warning" && level != "critical" && level != "major" {
			res.Message = fmt.Sprintf("The level is invalid: %s, only supports notice, warning, critical or major.", level)
			c.JSON(http.StatusBadRequest, errorcodev2.BadRequestWithMessage(requestID, res.Message))
			return
		}
	}

	// 1. 获取系统模板
	if templateType == "" || templateType == "system" {
		listTemplates := h.config.AlertTemplate()
		for _, template := range listTemplates.Rules {
			sysTemplates = append(sysTemplates, Template{
				AlertTemplateName: template.Alert,
				Expr:              intstr.FromString(template.Expr),
				For:               template.For,
				Type:              "system",
				Description:       template.Annotations.Description,
				//Level:             "systemDefault", // 假设系统模板有默认Level
			})
		}
	}

	// 2. 获取自定义模板
	alertTemplateName := ""
	if keywordType == "alertTemplateName" {
		alertTemplateName = keyword
	}
	alertTemplates, err := alert_template.QueryAlertTemplates(alertTemplateName, accountId)
	if err != nil {
		res.Message = "batch query alertingRecords from DB failed."
		logger.Errorf(ctx, "[QueryLimitByPageSize] from DB failed. errorInfo: %s", err.Error())
		c.JSON(http.StatusInternalServerError, errorcodev2.InternalServerErrorWithMessage(requestID, res.Message))
		return
	}

	// 处理自定义模板
	for _, alertTpl := range alertTemplates {

		// 检查模板名称是否匹配（包含或被包含）
		if alertTemplateName != "" {
			currentName := strings.ToLower(strings.TrimSpace(alertTpl.AlertTemplateName))
			searchName := strings.ToLower(alertTemplateName)

			// 如果两者互不包含，则跳过
			if !strings.Contains(currentName, searchName) && !strings.Contains(searchName, currentName) {
				continue
			}
		}

		var m vmv1beta1.VMRule
		if err := json.Unmarshal([]byte(alertTpl.AlertTemplateDetail), &m); err != nil {
			logger.Errorf(ctx, "JsonUnmarshal for alertingRuleRecord.AlertingRuleDetail failed: %v", err)
			continue
		}

		HideFields4AR(&m)

		if m.Spec.Groups[0].Rules[0].Annotations == nil {
			m.Spec.Groups[0].Rules[0].Annotations = make(map[string]string)
		}

		if m.Spec.Groups[0].Rules[0].Labels == nil {
			m.Spec.Groups[0].Rules[0].Labels = make(map[string]string)
		}

		descrip, ok := m.Spec.Groups[0].Rules[0].Annotations[cpromv1.DescriptionAnnotation]
		if !ok || descrip == "" { // 没有DescriptionAnnotation这个值或为空，给一个内置值
			m.Spec.Groups[0].Rules[0].Annotations[cpromv1.DescriptionAnnotation] = ""
		}

		message, ok := m.Spec.Groups[0].Rules[0].Annotations[cpromv1.MessageAnnotation]
		if !ok || message == "" { // 没有message这个值或为空，给一个内置值
			m.Spec.Groups[0].Rules[0].Annotations[cpromv1.MessageAnnotation] = ""
		}

		// 处理BoundInstances等逻辑
		// boundInstances := make([]string, 0)
		// if mappings, err := alert_template.QueryMappingByID(alertTpl.AlertTemplateID, accountId); err == nil {
		// 	for _, mapping := range mappings {
		// 		boundInstances = append(boundInstances, mapping.InstanceName)
		// 	}
		// }

		tpl := Template{
			AlertTemplateID:   alertTpl.AlertTemplateID,
			AlertTemplateName: alertTpl.AlertTemplateName,
			Expr:              m.Spec.Groups[0].Rules[0].Expr,
			For:               m.Spec.Groups[0].Rules[0].For,
			Description:       m.Spec.Groups[0].Rules[0].Annotations[cpromv1.DescriptionAnnotation],
			Message:           m.Spec.Groups[0].Rules[0].Annotations[cpromv1.MessageAnnotation],
			Type:              "custom",
			Version:           alertTpl.Version,
			Level:             alertTpl.Level,
			CreateTime:        alertTpl.CreateTime.UTC(),
			UpdateTime:        alertTpl.UpdateTime.UTC(),
			Labels:            m.Spec.Groups[0].Rules[0].Labels,
			Annotations:       m.Spec.Groups[0].Rules[0].Annotations,
			BoundInstances:    []string{},
		}

		removeKeys(tpl.Labels, cpromv1.NotifyRuleIDLabel, cpromv1.SeverityLabel)
		removeKeys(tpl.Annotations, cpromv1.DescriptionAnnotation, cpromv1.MessageAnnotation)
		customTemplates = append(customTemplates, tpl)
	}

	// 3.1 处理映射关系
	boundInstancesMap := make(map[string][]string)
	mappings, err := alert_template.QueryMappingByAccountId(accountId)
	if err != nil {
		logger.Errorf(ctx, "QueryMappingByAccountId failed. errorInfo=[%v]", err.Error())
		res.Message = "QueryMappingByAccountId failed."
		c.JSON(http.StatusInternalServerError, errorcodev2.InternalServerErrorWithMessage(requestID, res.Message))
		return
	}

	for _, mapping := range mappings {
		if _, ok := boundInstancesMap[mapping.AlertTemplateID]; !ok {
			boundInstancesMap[mapping.AlertTemplateID] = make([]string, 0)
		}
		boundInstancesMap[mapping.AlertTemplateID] = append(boundInstancesMap[mapping.AlertTemplateID], mapping.InstanceName)
	}

	for i := range customTemplates {
		if _, ok := boundInstancesMap[customTemplates[i].AlertTemplateID]; !ok {
			continue
		}
		customTemplates[i].BoundInstances = boundInstancesMap[customTemplates[i].AlertTemplateID]
	}

	// 3. 根据templateType合并模板
	var allTemplates []Template
	switch templateType {
	case "system":
		allTemplates = sysTemplates
	case "custom":
		allTemplates = customTemplates
	default:
		allTemplates = append(customTemplates, sysTemplates...)
	}

	// 4. 参数校验
	//if keyword != "" && keywordType == "" {
	//	res.Message = "缺少keywordType参数"
	//	c.JSON(http.StatusBadRequest, errorcodev2.BadRequestWithMessage(requestID, res.Message))
	//	return
	//}
	if keywordType != "" {
		if keywordType != "alertTemplateName" { // 扩展其他类型
			res.Message = fmt.Sprintf("The keywordType is invalid: %s, only supports alertTemplateName.", keywordType)
			c.JSON(http.StatusBadRequest, errorcodev2.BadRequestWithMessage(requestID, res.Message))
			return
		}
	}

	// 5. 组合筛选条件
	filteredTemplates := []Template{}
	for _, tpl := range allTemplates {
		keywordMatch := keywordType == "" || strings.Contains(tpl.AlertTemplateName, keyword) || keyword == ""
		levelMatch := level == "" || tpl.Level == level

		if keywordMatch && levelMatch {
			filteredTemplates = append(filteredTemplates, tpl)
		}
	}

	// 6. 分页
	totalCount := len(filteredTemplates)
	start := (pageNo - 1) * pageSize
	if start < 0 {
		start = 0
	}
	end := pageNo * pageSize
	if end > totalCount {
		end = totalCount
	}

	pagedTemplates := make([]Template, 0)
	if start <= end {
		pagedTemplates = filteredTemplates[start:end]
	} else {
		pagedTemplates = make([]Template, 0)
	}

	// 7. 返回结果
	listResp := models.ListResponse{
		KeywordType: keywordType,
		Keyword:     keyword,
		PageNo:      pageNo,
		PageSize:    pageSize,
		TotalCount:  totalCount,
		Items:       pagedTemplates,
		Order:       "desc",
		OrderBy:     "updateTime",
	}
	c.JSON(http.StatusOK, listResp)
}

// CreateTemplate 创建模板函数
func (h *AlertingRuleAPI) CreateTemplate(c *gin.Context) {
	var (
		ctx       = handler.NewContext(c)
		res       = errorcodev2.NewResponse()
		accountID = c.MustGet("accountID").(string)
		//instanceID = c.MustGet("instanceId").(string)
		//namespace  = instanceID

	)
	requestID := logger.GetRequestID(ctx)
	var req CreateTemplateRequest
	if err := c.Bind(&req); err != nil {
		err := fmt.Errorf("bind post data err: %v", err)
		logger.Errorf(ctx, "%v", err)

		res.Message = err.Error()
		c.JSON(http.StatusBadRequest, errorcodev2.BadRequestWithMessage(requestID, res.Message))
		return
	}

	if req.Message != "" {
		if !MessageRegex.MatchString(req.Message) {
			logger.Errorf(ctx, "%v", "Invalid AlertTemplate message")
			res.Message = "Invalid AlertTemplate message"
			c.JSON(http.StatusBadRequest, errorcodev2.BadRequestWithMessage(requestID, res.Message))
			return
		}
	}

	if req.Level != "notice" && req.Level != "warning" && req.Level != "critical" && req.Level != "major" {
		res.Message = fmt.Sprintf("The level is invalid: %s, only supports notice, warning, critical or major.", req.Level)
		c.JSON(http.StatusBadRequest, errorcodev2.BadRequestWithMessage(requestID, res.Message))
		return
	}

	if !TplNameRegex.MatchString(req.AlertTemplateName) {
		logger.Errorf(ctx, "%v", "Invalid AlertTemplateName")
		res.Message = "Invalid AlertTemplateName"
		c.JSON(http.StatusBadRequest, errorcodev2.BadRequestWithMessage(requestID, res.Message))
		return
	}
	// 重名校验
	req.AlertTemplateName = strings.Trim(req.AlertTemplateName, " ")
	_, errConflict := alert_template.QueryByName(req.AlertTemplateName, accountID)
	if errConflict == nil || checkSysAlertNameConflict(h.config, req.AlertTemplateName) {
		logger.Errorf(ctx, "AlertTemplateName already exist. notifyRuleName[%v]", req.AlertTemplateName)
		res.Message = "告警模板名称已存在，请检查！"
		c.JSON(http.StatusConflict, errorcodev2.StatusConflictWithMessage(requestID, req.AlertTemplateName))
		return
	}

	var templateId string
	var err error
	templateId, err = utils.GenerateUUIDForTpl(ctx, alertTemplatePrefix, h.config.ResourceClusterRegionID, accountID)
	if err != nil {
		res.Message = fmt.Sprintf("genNotifyRuleID err: %v", err)
		logger.Errorf(ctx, "%v", res.Message)
		c.JSON(http.StatusInternalServerError, errorcodev2.InternalServerErrorWithMessage(requestID, res.Message))
		return
	}

	m := vmv1beta1.VMRule{}
	m.Spec.Groups = make([]vmv1beta1.RuleGroup, 1)
	m.Spec.Groups[0].Rules = make([]vmv1beta1.Rule, 1)
	m.Spec.Groups[0].Rules[0].Labels = make(map[string]string)
	m.Spec.Groups[0].Rules[0].Annotations = make(map[string]string)
	m.Spec.Groups[0].Name = req.AlertTemplateName
	m.Spec.Groups[0].Rules[0].Alert = req.AlertTemplateName
	m.Spec.Groups[0].Rules[0].Expr = req.Expr
	m.Spec.Groups[0].Rules[0].For = req.For

	//filteredLabels := make(map[string]string)
	//filteredAnnotations := make(map[string]string)
	if len(req.Labels) > 0 {
		for k, _ := range req.Labels {
			if k == "" { // 检查键是否非空
				res.Message = "key can not be empty"
				logger.Errorf(ctx, "%v", res.Message)
				c.JSON(http.StatusBadRequest, errorcodev2.BadRequestWithMessage(requestID, res.Message))
				return
			}
		}
		m.Spec.Groups[0].Rules[0].Labels = req.Labels
	}
	if len(req.Annotations) > 0 {
		for k, _ := range req.Annotations {
			if k == "" { // 检查键是否非空
				res.Message = "key can not be empty"
				logger.Errorf(ctx, "%v", res.Message)
				c.JSON(http.StatusBadRequest, errorcodev2.BadRequestWithMessage(requestID, res.Message))
				return
			}
		}
		m.Spec.Groups[0].Rules[0].Annotations = req.Annotations
	}
	m.Spec.Groups[0].Rules[0].Labels[cpromv1.NotifyRuleIDLabel] = req.NotifyRuleID
	m.Spec.Groups[0].Rules[0].Labels["cprom_severity"] = req.Level
	m.Spec.Groups[0].Rules[0].Annotations[cpromv1.DescriptionAnnotation] = req.Description
	if req.Message != "" {
		m.Spec.Groups[0].Rules[0].Annotations[cpromv1.MessageAnnotation] = req.Message
	}

	// 从cookie里获取用户名
	userName, err := c.Cookie("bce-login-display-name")
	totalCookie, err := c.Cookie("") // 获取所有cookie
	if err != nil {
		logger.Warnf(ctx, "[CreateAlertingRuleHandler] get userName from Cookie failed. totalCookie=[%v], errorInfo=[%v]", totalCookie, err.Error())
		// 兼容混合云调用接口没有传cookie的情况，不直接报错返回，指定userName为admin
		userName = "admin"
	}

	if m.Labels == nil {
		m.Labels = map[string]string{}
	}

	m.Labels[cpromv1.BCEAccountIDLabel] = accountID
	m.Name = templateId

	// 校验rule的expr、labels、annotations
	isValidate, errMsg := validateRule(m.Spec.Groups[0].Rules[0])
	if !isValidate {
		res.Message = "validate rule failed: " + errMsg
		logger.Errorf(ctx, "%v", res.Message)
		c.JSON(http.StatusBadRequest, errorcodev2.BadRequestWithMessage(requestID, res.Message))
		return
	}

	// 1、持久化存储：将vmRule结构体转换为json字符串存入mysql
	vmRuleBytes, err := json.Marshal(&m) // 注意，此时m.Namespace为instanceID。目前针对双写情况ns=cprom-alert
	if err != nil {
		res.Message = fmt.Sprintf("json.Marshal alertingRule failed. namespace:%v, alertingRuleName:%v err: %v", m.Namespace, m.Name, err)
		logger.Errorf(ctx, "%v", res.Message)
		c.JSON(http.StatusBadRequest, errorcodev2.BadRequestWithMessage(requestID, res.Message))
		return
	}
	alertingTplRecord := model.AlertTemplate{
		BCEAccountID:        accountID,
		AlertTemplateID:     templateId,
		AlertTemplateName:   req.AlertTemplateName,
		Level:               req.Level,
		AlertTemplateDetail: string(vmRuleBytes), // 此时的vmRuleBytes中不包含Create后m.Metadata中新填充的resourceVersion、selfLink等字段，以后也不会包含
		Version:             "v1",
		Operator:            userName,
	}
	err = alert_template.Insert(alertingTplRecord)
	if err != nil {
		logger.Errorf(ctx, "[InsertAlertingRule] to DB failed. errorInfo=[%v]", err.Error())
		res.Message = fmt.Sprintf("insert record to DB failed.")
		c.JSON(http.StatusInternalServerError, errorcodev2.InternalServerErrorWithMessage(requestID, res.Message))
		return
	}
	result := gin.H{"alertTemplateId": templateId}
	c.JSON(http.StatusOK, result)
}

// GetTemplate 获取告警模板信息
func (h *AlertingRuleAPI) GetTemplate(c *gin.Context) {
	var (
		ctx             = handler.NewContext(c)
		res             = errorcodev2.NewResponse()
		accountID       = c.MustGet("accountID").(string)
		alertTemplateID = c.Param("TplID")
		//instanceID = c.MustGet("instanceId").(string)
		//namespace  = instanceID
	)

	requestID := logger.GetRequestID(ctx)

	alertTemplate, err := alert_template.QueryById(alertTemplateID, accountID)
	if err != nil {
		res.Message = fmt.Sprintf("not found alertingRuleTpl. "+
			"accountID=[%v], alertingRuleTpl=[%v]", accountID, alertTemplateID)
		logger.Errorf(ctx, res.Message)
		c.JSON(http.StatusNotFound, errorcodev2.NotFoundErrorWithMessage(requestID, alertTemplateID))
		c.Abort()
		return
	}

	// 1、将AlertingRuleDetail部分转化为结构体
	m := vmv1beta1.VMRule{}
	err = json.Unmarshal([]byte(alertTemplate.AlertTemplateDetail), &m)
	if err != nil {
		logger.Errorf(ctx, "JsonUnmarshal for alertingRuleRecord.AlertingRuleDetail failed. errorInfo=[%v]", err.Error())
		res.Message = fmt.Sprintf("get record from DB failed.")
		c.JSON(http.StatusInternalServerError, errorcodev2.InternalServerErrorWithMessage(requestID, res.Message))
		return
	}
	// 2、按照原有处理方式处理m结构
	HideFields4AR(&m)

	//severity, ok := m.Spec.Groups[0].Rules[0].Labels[cpromv1.SeverityLabel]
	//if !ok || severity == "" { // 老策略中没有SeverityLabel这个值或为空，给一个内置值
	//	m.Spec.Groups[0].Rules[0].Labels[cpromv1.SeverityLabel] = "notice"
	//}

	if m.Spec.Groups[0].Rules[0].Annotations == nil {
		m.Spec.Groups[0].Rules[0].Annotations = make(map[string]string)
	}

	if m.Spec.Groups[0].Rules[0].Labels == nil {
		m.Spec.Groups[0].Rules[0].Labels = make(map[string]string)
	}

	describe, ok := m.Spec.Groups[0].Rules[0].Annotations[cpromv1.DescriptionAnnotation]
	if !ok || describe == "" { // 老策略中没有description这个值或为空，给一个内置值
		m.Spec.Groups[0].Rules[0].Annotations[cpromv1.DescriptionAnnotation] = ""
	}

	message, ok := m.Spec.Groups[0].Rules[0].Annotations[cpromv1.MessageAnnotation]
	if !ok || message == "" { // 老策略中没有message这个值或为空，给一个内置值
		m.Spec.Groups[0].Rules[0].Annotations[cpromv1.MessageAnnotation] = ""
	}

	tmplDetail := TemplateDetail{
		AlertTemplateName: alertTemplate.AlertTemplateName,
		Expr:              m.Spec.Groups[0].Rules[0].Expr,
		For:               m.Spec.Groups[0].Rules[0].For,
		Description:       m.Spec.Groups[0].Rules[0].Annotations[cpromv1.DescriptionAnnotation],
		Message:           m.Spec.Groups[0].Rules[0].Annotations[cpromv1.MessageAnnotation],
		Level:             alertTemplate.Level,
		CreateTime:        alertTemplate.CreateTime.UTC(),
		UpdateTime:        alertTemplate.UpdateTime.UTC(),
		Version:           alertTemplate.Version,
		Labels:            m.Spec.Groups[0].Rules[0].Labels,
		Annotations:       m.Spec.Groups[0].Rules[0].Annotations,
	}
	removeKeys(tmplDetail.Labels, cpromv1.NotifyRuleIDLabel, cpromv1.SeverityLabel)
	removeKeys(tmplDetail.Annotations, cpromv1.DescriptionAnnotation, cpromv1.MessageAnnotation)

	c.JSON(http.StatusOK, tmplDetail)
	return
}

// UpdateTemplate 更新模板函数
func (h *AlertingRuleAPI) UpdateTemplate(c *gin.Context) {
	var (
		ctx       = handler.NewContext(c)
		res       = errorcodev2.NewResponse()
		accountID = c.MustGet("accountID").(string)
		//instanceID = c.MustGet("instanceId").(string)
		//namespace  = instanceID
		alertTemplateID = c.Param("TplID")
		mu              = vmv1beta1.VMRule{}
	)
	requestID := logger.GetRequestID(ctx)
	var req CreateTemplateRequest
	if err := c.Bind(&req); err != nil {
		err := fmt.Errorf("bind post data err: %v", err)
		logger.Errorf(ctx, "%v", err)

		res.Message = err.Error()
		c.JSON(http.StatusBadRequest, errorcodev2.BadRequestWithMessage(requestID, res.Message))
		return
	}

	if req.Message != "" {
		if !MessageRegex.MatchString(req.Message) {
			logger.Errorf(ctx, "%v", "Invalid AlertTemplate message")
			res.Message = "Invalid AlertTemplate message"
			c.JSON(http.StatusBadRequest, errorcodev2.BadRequestWithMessage(requestID, res.Message))
			return
		}
	}

	if req.Level != "notice" && req.Level != "warning" && req.Level != "critical" && req.Level != "major" {
		res.Message = fmt.Sprintf("The level is invalid: %s, only supports notice, warning, critical or major.", req.Level)
		c.JSON(http.StatusBadRequest, errorcodev2.BadRequestWithMessage(requestID, res.Message))
		return
	}

	if !TplNameRegex.MatchString(req.AlertTemplateName) {
		logger.Errorf(ctx, "%v", "Invalid AlertTemplateName")
		res.Message = "Invalid AlertTemplateName"
		c.JSON(http.StatusBadRequest, errorcodev2.BadRequestWithMessage(requestID, res.Message))
		return
	}

	// 重名校验
	req.AlertTemplateName = strings.Trim(req.AlertTemplateName, " ")
	tpl, errConflict := alert_template.QueryByName(req.AlertTemplateName, accountID)
	if errConflict == nil && alertTemplateID != tpl.AlertTemplateID {
		logger.Errorf(ctx, "AlertTemplateName already exist. notifyRuleName[%v]", req.AlertTemplateName)
		res.Message = "告警模板名称已存在，请检查！"
		c.JSON(http.StatusConflict, errorcodev2.StatusConflictWithMessage(requestID, req.AlertTemplateName))
		return
	}
	if checkSysAlertNameConflict(h.config, req.AlertTemplateName) {
		logger.Errorf(ctx, "AlertTemplateName already exist. notifyRuleName[%v]", req.AlertTemplateName)
		res.Message = "告警模板名称已存在，请检查！"
		c.JSON(http.StatusConflict, errorcodev2.StatusConflictWithMessage(requestID, req.AlertTemplateName))
		return
	}

	mu.Spec.Groups = make([]vmv1beta1.RuleGroup, 1)
	mu.Spec.Groups[0].Rules = make([]vmv1beta1.Rule, 1)
	mu.Spec.Groups[0].Rules[0].Labels = make(map[string]string)      // 初始化 Labels map
	mu.Spec.Groups[0].Rules[0].Annotations = make(map[string]string) // 初始化 Annotations map
	mu.Spec.Groups[0].Name = req.AlertTemplateName
	mu.Spec.Groups[0].Rules[0].Alert = req.AlertTemplateName
	mu.Spec.Groups[0].Rules[0].Expr = req.Expr
	mu.Spec.Groups[0].Rules[0].For = req.For

	if len(req.Labels) > 0 {
		for k, _ := range req.Labels {
			if k == "" { // 检查键是否非空
				res.Message = "key can not be empty"
				logger.Errorf(ctx, "%v", res.Message)
				c.JSON(http.StatusBadRequest, errorcodev2.BadRequestWithMessage(requestID, res.Message))
				return
			}
		}
		mu.Spec.Groups[0].Rules[0].Labels = req.Labels
	}

	if len(req.Annotations) > 0 {
		for k, _ := range req.Annotations {
			if k == "" { // 检查键是否非空
				res.Message = "key can not be empty"
				logger.Errorf(ctx, "%v", res.Message)
				c.JSON(http.StatusBadRequest, errorcodev2.BadRequestWithMessage(requestID, res.Message))
				return
			}
		}
		mu.Spec.Groups[0].Rules[0].Annotations = req.Annotations
	}
	mu.Spec.Groups[0].Rules[0].Labels[cpromv1.NotifyRuleIDLabel] = req.NotifyRuleID
	mu.Spec.Groups[0].Rules[0].Labels["cprom_severity"] = req.Level
	mu.Spec.Groups[0].Rules[0].Annotations[cpromv1.DescriptionAnnotation] = req.Description
	if req.Message != "" {
		mu.Spec.Groups[0].Rules[0].Annotations[cpromv1.MessageAnnotation] = req.Message
	}

	// 校验rule的expr、labels、annotations
	isValidate, errMsg := validateRule(mu.Spec.Groups[0].Rules[0])
	if !isValidate {
		res.Message = errMsg
		c.JSON(http.StatusBadRequest, errorcodev2.BadRequestWithMessage(requestID, res.Message))
		return
	}

	alertTemplate, err := alert_template.QueryById(alertTemplateID, accountID)
	if err != nil {
		res.Message = fmt.Sprintf("not found alertingRuleTemplate. "+
			"accountID=[%v], alertingRuleTemplate=[%v]", accountID, alertTemplateID)
		logger.Errorf(ctx, res.Message)
		c.JSON(http.StatusNotFound, errorcodev2.NotFoundErrorWithMessage(requestID, alertTemplateID))
		c.Abort()
		return
	}

	// 0、将从mysql中查询的记录转化为k8s的结构，
	m := vmv1beta1.VMRule{} // alertingRule的旧记录
	err = json.Unmarshal([]byte(alertTemplate.AlertTemplateDetail), &m)
	if err != nil {
		logger.Errorf(ctx, "json.unmarshal for alertingRuleRecord.AlertingRuleDetail failed. errorInfo=[%v]", err.Error())
		res.Message = fmt.Sprintf("update alertingRule failed. erroInfo=[json.unmarshal record from DB failed]")
		c.JSON(http.StatusInternalServerError, errorcodev2.InternalServerErrorWithMessage(requestID, res.Message))
		return
	}
	// 用待更新的alertingRule配置替换旧记录的部分字段
	m.Spec = mu.Spec

	// 1、持久化存储：将vmRule结构体转换为json字符串存入mysql
	vmRuleBytes, err := json.Marshal(&m) // 注意，此时m.Namespace为instanceID。目前针对双写情况ns=cprom-alert
	if err != nil {
		res.Message = fmt.Sprintf("json.Marshal alertingRule failed. namespace:%v, alertingRuleName:%v err: %v", m.Namespace, m.Name, err)
		logger.Errorf(ctx, "%v", res.Message)
		c.JSON(http.StatusBadRequest, errorcodev2.BadRequestWithMessage(requestID, res.Message))
		return
	}

	alertingTplRecord := model.AlertTemplate{
		BCEAccountID:        accountID,
		AlertTemplateID:     alertTemplateID,
		AlertTemplateName:   req.AlertTemplateName,
		Level:               req.Level,
		AlertTemplateDetail: string(vmRuleBytes), // 此时的vmRuleBytes中不包含Create后m.Metadata中新填充的resourceVersion、selfLink等字段，以后也不会包含
		Version:             incrementVersion(alertTemplate.Version),
		Operator:            alertTemplate.Operator,
	}
	err = alert_template.Update(alertingTplRecord)
	if err != nil {
		logger.Errorf(ctx, "[UpdateAlertingRule] to DB failed. errorInfo=[%v]", err.Error())
		res.Message = fmt.Sprintf("update record to DB failed.")
		c.JSON(http.StatusInternalServerError, errorcodev2.InternalServerErrorWithMessage(requestID, res.Message))
		return
	}
	logger.Infof(ctx, "update alertingTPL in DB successful")

	c.JSON(http.StatusOK, "success")
}

// DeleteTemplate 删除模板
func (h *AlertingRuleAPI) DeleteTemplate(c *gin.Context) {
	var (
		ctx       = handler.NewContext(c)
		res       = errorcodev2.NewResponse()
		accountID = c.MustGet("accountID").(string)
		//instanceID = c.MustGet("instanceId").(string)
		//namespace  = instanceID
		needDeleteAlert = c.Query("deleteAlert")
	)
	requestID := logger.GetRequestID(ctx)
	var req DeleteTemplateRequest
	if err := c.Bind(&req); err != nil {
		err := fmt.Errorf("bind post data err: %v", err)
		logger.Errorf(ctx, "%v", err)

		res.Message = err.Error()
		c.JSON(http.StatusBadRequest, errorcodev2.BadRequestWithMessage(requestID, res.Message))
		return
	}

	for _, tplID := range req.AlertTemplateIds {
		err := alert_template.Delete(tplID, accountID)
		if err != nil {
			logger.Errorf(ctx, "[DeleteAlertingRule] to DB failed. errorInfo=[%v]", err.Error())
			//res.Message = fmt.Sprintf("delete record to DB failed.")
			//c.JSON(http.StatusInternalServerError, errorcodev2.InternalServerErrorWithMessage(requestID, res.Message))
			continue
		}

		logger.Infof(ctx, "delete alert_template success: %v", tplID)
		//删除对应告警规则
		if needDeleteAlert == "true" {
			alertTemplateMappings, err := alert_template.QueryMappingByID(tplID, accountID)
			if err != nil {
				res.Message = fmt.Sprintf("batch query alertTemplateMappings from DB failed.")
				logger.Errorf(ctx, "[alertTemplateMappings] from DB failed. errorInfo=%s", err.Error())
				c.JSON(http.StatusInternalServerError, errorcodev2.InternalServerErrorWithMessage(requestID, res.Message))
				return
			}

			for _, mapping := range alertTemplateMappings {
				retryPolicy := retrypolicy.NewIntervalRetryPolicy(ctx, 3, 1)
				cpromRegion := getProcessedRegion(h.config.Region, mapping.Region)
				cpromClient := cprom.NewClient(&bce.Config{
					Checksum:    true,
					Timeout:     time.Second * 5,
					Region:      cpromRegion,
					Endpoint:    cprom.Endpoints[cpromRegion],
					RetryPolicy: retryPolicy},
				)
				err = cpromClient.DeleteAlertingRule(ctx, mapping.InstanceID, mapping.AlertRuleID, h.stsClient.NewSignOption(ctx, accountID))
				if err != nil {
					logger.Errorf(ctx, "delete alerting rule err: %v", err)
					continue
				}
			}
		}

		err = alert_template.DeleteMappingByTplIDAndAccountID(tplID, accountID)
		if err != nil {
			logger.Errorf(ctx, "[DeleteAlertMapping] to DB failed. errorInfo=[%v]", err.Error())
			continue
		}
		logger.Infof(ctx, "delete template mapping success: %v", tplID)
	}

	c.JSON(http.StatusOK, "success")
}

// ApplyTemplate 应用模板到实例上
func (h *AlertingRuleAPI) ApplyTemplate(c *gin.Context) {
	var (
		ctx        = handler.NewContext(c)
		res        = errorcodev2.NewResponse()
		accountID  = c.MustGet("accountID").(string)
		instanceID = c.Query("instanceId")
		//namespace  = instanceID
		//mi              = c.MustGet("instance").(*cpromv1.MonitorInstance)
		alertTemplateID = c.Param("TplID")
		Action          = c.Query("action")
	)
	requestID := logger.GetRequestID(ctx)
	//TODO:
	if instanceID == "" {
		err := fmt.Errorf("instanceID is nil")
		logger.Errorf(ctx, "%v", err)
		res.Message = err.Error()
		c.JSON(http.StatusBadRequest, errorcodev2.BadRequestWithMessage(requestID, res.Message))
		return
	}
	var req ApplyTemplateRequest
	if err := c.Bind(&req); err != nil {
		err := fmt.Errorf("bind post data err: %v", err)
		logger.Errorf(ctx, "%v", err)

		res.Message = err.Error()
		c.JSON(http.StatusBadRequest, errorcodev2.BadRequestWithMessage(requestID, res.Message))
		return
	}
	req.InstanceID = instanceID
	alertTemplate, err := alert_template.QueryById(alertTemplateID, accountID)
	if err != nil {
		res.Message = fmt.Sprintf("not found alertingRuleTpl. "+
			"accountID=[%v], alertingRuleTpl=[%v]", accountID, alertTemplateID)
		logger.Errorf(ctx, res.Message)
		c.JSON(http.StatusNotFound, errorcodev2.NotFoundErrorWithMessage(requestID, alertTemplateID))
		c.Abort()
		return
	}

	// 1、将AlertingRuleDetail部分转化为结构体
	m := vmv1beta1.VMRule{}
	err = json.Unmarshal([]byte(alertTemplate.AlertTemplateDetail), &m)
	if err != nil {
		logger.Errorf(ctx, "JsonUnmarshal for alertingRuleRecord.AlertingRuleDetail failed. errorInfo=[%v]", err.Error())
		res.Message = fmt.Sprintf("get record from DB failed.")
		c.JSON(http.StatusInternalServerError, errorcodev2.InternalServerErrorWithMessage(requestID, res.Message))
		return
	}
	// 2、按照原有处理方式处理m结构
	HideFields4AR(&m)

	if m.Spec.Groups[0].Rules[0].Annotations == nil {
		m.Spec.Groups[0].Rules[0].Annotations = make(map[string]string)
	}

	if m.Spec.Groups[0].Rules[0].Labels == nil {
		m.Spec.Groups[0].Rules[0].Labels = make(map[string]string)
	}

	severity, ok := m.Spec.Groups[0].Rules[0].Labels[cpromv1.SeverityLabel]
	if !ok || severity == "" { // 老策略中没有SeverityLabel这个值或为空，给一个内置值
		m.Spec.Groups[0].Rules[0].Labels[cpromv1.SeverityLabel] = "notice"
	}

	describe, ok := m.Spec.Groups[0].Rules[0].Annotations[cpromv1.DescriptionAnnotation]
	if !ok || describe == "" { // 老策略中没有SeverityLabel这个值或为空，给一个内置值
		m.Spec.Groups[0].Rules[0].Annotations[cpromv1.DescriptionAnnotation] = "none"
	}

	filteredLabels := make(map[string]string)
	filteredAnnotations := make(map[string]string)
	if len(m.Spec.Groups[0].Rules[0].Labels) > 0 {
		for k, v := range m.Spec.Groups[0].Rules[0].Labels {
			if k != "" { // 检查键是否非空
				filteredLabels[k] = v
			}
		}
		m.Spec.Groups[0].Rules[0].Labels = filteredLabels
	}

	if len(m.Spec.Groups[0].Rules[0].Annotations) > 0 {
		for k, v := range m.Spec.Groups[0].Rules[0].Annotations {
			if k != "" { // 检查键是否非空
				filteredAnnotations[k] = v
			}
		}
		m.Spec.Groups[0].Rules[0].Annotations = filteredAnnotations
	}

	tmplDetail := TemplateDetail{
		AlertTemplateName: m.Spec.Groups[0].Rules[0].Alert,
		Expr:              m.Spec.Groups[0].Rules[0].Expr,
		For:               m.Spec.Groups[0].Rules[0].For,
		Description:       m.Spec.Groups[0].Rules[0].Annotations[cpromv1.DescriptionAnnotation],
		Message:           m.Spec.Groups[0].Rules[0].Annotations[cpromv1.MessageAnnotation],
		Level:             m.Spec.Groups[0].Rules[0].Labels[cpromv1.SeverityLabel],
		NotifyRuleID:      "notify-default",
		Version:           alertTemplate.Version,
		Labels:            m.Spec.Groups[0].Rules[0].Labels,
		Annotations:       m.Spec.Groups[0].Rules[0].Annotations,
	}

	if Action != "" {
		switch Action {
		case "Bind":
			if err = h.TplBindInstance(ctx, alertTemplateID, req, accountID, tmplDetail); err != nil {
				err = fmt.Errorf(" %v", err)
				logger.Errorf(ctx, "%v", err)

				res.Message = err.Error()
				c.JSON(http.StatusForbidden, errorcodev2.AccessDeniedErrorWithMessage(requestID, res.Message))
				return
			}
		case "Sync":
			if err = h.TplSyncInstance(ctx, alertTemplateID, req, accountID, tmplDetail); err != nil {
				err = fmt.Errorf(" %v", err)
				logger.Errorf(ctx, "%v", err)

				res.Message = err.Error()
				c.JSON(http.StatusForbidden, errorcodev2.AccessDeniedErrorWithMessage(requestID, res.Message))
				return
			}
		case "Unbind":
			if err = h.TplUnbindInstance(ctx, alertTemplateID, req, accountID); err != nil {
				err = fmt.Errorf(" %v", err)
				logger.Errorf(ctx, "%v", err)

				res.Message = err.Error()
				c.JSON(http.StatusForbidden, errorcodev2.AccessDeniedErrorWithMessage(requestID, res.Message))
				return
			}
		default:
			res.Message = fmt.Sprintf("not support action: %v", Action)
			logger.Errorf(ctx, res.Message)
			c.JSON(http.StatusBadRequest, errorcodev2.BadRequestWithMessage(requestID, res.Message))
			return
		}

	}

	c.JSON(http.StatusOK, "success")
}

// QueryTemplateBindings 根据模板ID查询绑定的实例列表，包含实例名称、同步时间和当前版本等信息。
func (h *AlertingRuleAPI) QueryTemplateBindings(c *gin.Context) {
	var (
		ctx             = handler.NewContext(c)
		res             = errorcodev2.NewResponse()
		alertTemplateID = c.Param("TplID")
		accountID       = c.MustGet("accountID").(string)
		region          = c.Query("region")
		//instanceID = c.MustGet("instanceId").(string)
		//namespace  = instanceID

	)
	requestID := logger.GetRequestID(ctx)
	var req QueryBindingRequest
	req.Region = region

	// 拿到template
	alertTemplate, err := alert_template.QueryById(alertTemplateID, accountID)
	if err != nil {
		res.Message = fmt.Sprintf("not found alertingRuleTpl. "+
			"accountID=[%v], alertingRuleTpl=[%v]", accountID, alertTemplateID)
		logger.Errorf(ctx, res.Message)
		c.JSON(http.StatusNotFound, errorcodev2.NotFoundErrorWithMessage(requestID, alertTemplateID))
		c.Abort()
		return
	}

	retryPolicy := retrypolicy.NewIntervalRetryPolicy(ctx, 3, 1)
	cpromRegion := getProcessedRegion(h.config.Region, req.Region)
	cpromClient := cprom.NewClient(&bce.Config{
		Checksum:    true,
		Timeout:     time.Second * 5,
		Region:      cpromRegion,
		Endpoint:    cprom.Endpoints[cpromRegion],
		RetryPolicy: retryPolicy},
	)

	// 获取实例列表
	resp, err := cpromClient.ListInstance(ctx, 1, 100, h.stsClient.NewSignOption(ctx, accountID))
	if err != nil {
		logger.Errorf(ctx, "create alerting rule err: %v", err)
		res.Message = err.Error()
		c.JSON(http.StatusInternalServerError, errorcodev2.InternalServerErrorWithMessage(requestID, res.Message))
		return
	}

	instanceInfos := resp.Instances
	bindingDetails := make([]BindingDetail, 0)

	// 把关系表数据提取出来查询，放在for循环中性能有问题
	mappings, err := alert_template.QueryMappingByID(alertTemplateID, accountID)
	if err != nil {
		logger.Errorf(ctx, "[QueryTemplateBindings]Query alertTplMapping from DB failed. errorInfo=[%v]", err.Error())
		res.Message = err.Error()
		c.JSON(http.StatusInternalServerError, errorcodev2.InternalServerErrorWithMessage(requestID, res.Message))
		return
	}
	// 组装一下数据，以instanceId为key，方便后续查询
	mappingMap := make(map[string]*model.TemplateInstanceMapping)
	for _, mapping := range mappings {
		mappingMap[mapping.InstanceID] = &mapping
	}

	for _, instance := range instanceInfos {
		syncTime := ""
		curVersion := ""
		needUpdate := false

		if mapping, ok := mappingMap[instance.InstanceID]; ok {
			syncTime = mapping.BindTime.UTC().Format(time.RFC3339)
			curVersion = mapping.Version
			needUpdate = alertTemplate.Version != mapping.Version
		}

		binding := BindingDetail{
			InstanceID:     instance.InstanceID,
			InstanceName:   instance.InstanceName,
			SyncTime:       syncTime,
			CurrentVersion: curVersion,
			NeedUpdate:     needUpdate,
		}
		bindingDetails = append(bindingDetails, binding)
	}

	bindingStatus := BindingStatus{
		BindingStatus: bindingDetails,
	}

	c.JSON(http.StatusOK, bindingStatus)
}

// BatchExportTemplate 导出多个告警模板的信息
func (h *AlertingRuleAPI) BatchExportTemplate(c *gin.Context) {
	var (
		ctx       = handler.NewContext(c)
		res       = errorcodev2.NewResponse()
		accountID = c.MustGet("accountID").(string)
		//instanceID = c.MustGet("instanceId").(string)
		//namespace  = instanceID

	)
	requestID := logger.GetRequestID(ctx)
	var req ExportRequest
	if err := c.Bind(&req); err != nil {
		err := fmt.Errorf("bind post data err: %v", err)
		logger.Errorf(ctx, "%v", err)

		res.Message = err.Error()
		c.JSON(http.StatusBadRequest, errorcodev2.BadRequestWithMessage(requestID, res.Message))
		return
	}

	items := make([]vmv1beta1.RuleGroup, 0)

	for _, tplID := range req.AlertTemplateIds {
		alertTemplate, err := alert_template.QueryById(tplID, accountID)
		if err != nil {
			res.Message = fmt.Sprintf("not found alertingRuleTpl. "+
				"accountID=[%v], alertingRuleTpl=[%v]", accountID, tplID)
			logger.Errorf(ctx, res.Message)
			c.JSON(http.StatusNotFound, errorcodev2.NotFoundErrorWithMessage(requestID, tplID))
			c.Abort()
			return
		}
		// 1、将AlertingRuleDetail部分转化为结构体
		m := vmv1beta1.VMRule{}
		err = json.Unmarshal([]byte(alertTemplate.AlertTemplateDetail), &m)
		if err != nil {
			logger.Errorf(ctx, "JsonUnmarshal for alertingRuleRecord.AlertingRuleDetail failed. errorInfo=[%v]", err.Error())
			res.Message = fmt.Sprintf("get record from DB failed.")
			c.JSON(http.StatusInternalServerError, errorcodev2.InternalServerErrorWithMessage(requestID, res.Message))
			return
		}
		// 2、按照原有处理方式处理m结构
		HideFields4AR(&m)

		if m.Spec.Groups[0].Rules[0].Labels == nil {
			m.Spec.Groups[0].Rules[0].Labels = map[string]string{}
		}
		if m.Spec.Groups[0].Rules[0].Annotations == nil {
			m.Spec.Groups[0].Rules[0].Annotations = map[string]string{}
		}

		severity, ok := m.Spec.Groups[0].Rules[0].Labels[cpromv1.SeverityLabel]
		if !ok || severity == "" { // 老策略中没有SeverityLabel这个值或为空，给一个内置值
			m.Spec.Groups[0].Rules[0].Labels[cpromv1.SeverityLabel] = "notice"
		}

		notifyRuleID, ok := m.Spec.Groups[0].Rules[0].Labels["cprom_notify_rule_id"]
		if !ok || notifyRuleID == "" { // 老策略中没有cprom_notify_rule_id这个值或为空，给一个内置值
			m.Spec.Groups[0].Rules[0].Labels["cprom_notify_rule_id"] = "notify-default"
		}

		message, ok := m.Spec.Groups[0].Rules[0].Annotations[cpromv1.MessageAnnotation]
		if ok && message == "" {
			delete(m.Spec.Groups[0].Rules[0].Annotations, cpromv1.MessageAnnotation)
		}

		vmrule := vmv1beta1.Rule{
			Alert:       alertTemplate.AlertTemplateName,
			Expr:        m.Spec.Groups[0].Rules[0].Expr,
			For:         m.Spec.Groups[0].Rules[0].For,
			Labels:      m.Spec.Groups[0].Rules[0].Labels,
			Annotations: m.Spec.Groups[0].Rules[0].Annotations,
		}
		Group := vmv1beta1.RuleGroup{
			Name:  alertTemplate.AlertTemplateName,
			Rules: []vmv1beta1.Rule{vmrule},
		}
		items = append(items, Group)
	}

	vmRuleSpec := vmv1beta1.VMRuleSpec{
		Groups: items,
	}
	c.JSON(http.StatusOK, vmRuleSpec)
}

// BatchImportTemplate 导入模版函数，用于批量导入告警模版
func (h *AlertingRuleAPI) BatchImportTemplate(c *gin.Context) {
	var (
		ctx       = handler.NewContext(c)
		res       = errorcodev2.NewResponse()
		accountID = c.MustGet("accountID").(string)
		//instanceID = c.MustGet("instanceId").(string)
		//namespace  = instanceID

	)
	requestID := logger.GetRequestID(ctx)
	var req ImportRequest
	if err := c.Bind(&req); err != nil {
		err := fmt.Errorf("bind post data err: %v", err)
		logger.Errorf(ctx, "%v", err)

		res.Message = err.Error()
		c.JSON(http.StatusBadRequest, errorcodev2.BadRequestWithMessage(requestID, res.Message))
		return
	}

	result := make([]string, 0)

	for _, ruleGroup := range req.Groups {
		for _, rule := range ruleGroup.Rules {
			if !TplNameRegex.MatchString(rule.Alert) {
				logger.Errorf(ctx, "%v", "Invalid AlertTemplateName")
				res.Message = "Invalid AlertTemplateName"
				c.JSON(http.StatusBadRequest, errorcodev2.BadRequestWithMessage(requestID, res.Message))
				return
			}
			//if rule.Expr == "" {
			//	res.Message = "alert expr is empty"
			//}
			m := vmv1beta1.VMRule{}
			m.Spec.Groups = make([]vmv1beta1.RuleGroup, 1)
			m.Spec.Groups[0].Rules = make([]vmv1beta1.Rule, 1)
			m.Spec.Groups[0].Rules[0].Labels = make(map[string]string)
			m.Spec.Groups[0].Rules[0].Annotations = make(map[string]string)
			m.Spec.Groups[0].Name = ruleGroup.Name
			m.Spec.Groups[0].Rules[0].Alert = rule.Alert
			m.Spec.Groups[0].Rules[0].Expr = rule.Expr
			m.Spec.Groups[0].Rules[0].For = rule.For
			//if len(rule.Annotations) > 0 {
			//	m.Spec.Groups[0].Rules[0].Annotations = rule.Annotations
			//}

			if len(rule.Labels) > 0 {
				for k, _ := range rule.Labels {
					if k == "" { // 检查键是否非空
						res.Message = "key can not be empty"
						logger.Errorf(ctx, "%v", res.Message)
						c.JSON(http.StatusBadRequest, errorcodev2.BadRequestWithMessage(requestID, res.Message))
						return
					}
				}
				m.Spec.Groups[0].Rules[0].Labels = rule.Labels
			}

			if len(rule.Annotations) > 0 {
				for k, _ := range rule.Annotations {
					if k == "" { // 检查键是否非空
						res.Message = "key can not be empty"
						logger.Errorf(ctx, "%v", res.Message)
						c.JSON(http.StatusBadRequest, errorcodev2.BadRequestWithMessage(requestID, res.Message))
						return
					}
				}
				m.Spec.Groups[0].Rules[0].Annotations = rule.Annotations
			}

			// 校验rule的expr、labels、annotations
			isValidate, errMsg := validateRule(m.Spec.Groups[0].Rules[0])
			if !isValidate {
				res.Message = "validate rule failed: " + errMsg
				c.JSON(http.StatusBadRequest, errorcodev2.BadRequestWithMessage(requestID, res.Message))
				return
			}
			// 从cookie里获取用户名
			userName, err := c.Cookie("bce-login-display-name")
			totalCookie, err := c.Cookie("") // 获取所有cookie
			if err != nil {
				logger.Warnf(ctx, "[CreateAlertingRuleHandler] get userName from Cookie failed. totalCookie=[%v], errorInfo=[%v]", totalCookie, err.Error())
				// 兼容混合云调用接口没有传cookie的情况，不直接报错返回，指定userName为admin
				userName = "admin"
			}

			var templateId string
			//var err error
			templateId, err = utils.GenerateUUIDForTpl(ctx, alertTemplatePrefix, h.config.ResourceClusterRegionID, accountID)
			if err != nil {
				res.Message = fmt.Sprintf("genNotifyRuleID err: %v", err)
				logger.Errorf(ctx, "%v", res.Message)
				//c.JSON(http.StatusInternalServerError, errorcodev2.InternalServerErrorWithMessage(requestID, res.Message))
				continue
			}

			if m.Labels == nil {
				m.Labels = map[string]string{}
			}

			m.Labels[cpromv1.BCEAccountIDLabel] = accountID
			m.Name = templateId

			severity, ok := m.Spec.Groups[0].Rules[0].Labels[cpromv1.SeverityLabel]
			if !ok || severity == "" { // 没有SeverityLabel这个值或为空，给一个内置值
				m.Spec.Groups[0].Rules[0].Labels[cpromv1.SeverityLabel] = "notice"
			}

			// 持久化存储：将vmRule结构体转换为json字符串存入mysql
			vmRuleBytes, err := json.Marshal(&m) // 注意，此时m.Namespace为instanceID。目前针对双写情况ns=cprom-alert
			if err != nil {
				res.Message = fmt.Sprintf("json.Marshal alertingRule failed. namespace:%v, alertingRuleName:%v err: %v", m.Namespace, m.Name, err)
				logger.Errorf(ctx, "%v", res.Message)
				//c.JSON(http.StatusBadRequest, errorcodev2.BadRequestWithMessage(requestID, res.Message))
				continue
			}

			alertingTplRecord := model.AlertTemplate{
				BCEAccountID:        accountID,
				AlertTemplateID:     templateId,
				AlertTemplateName:   rule.Alert,
				Level:               m.Spec.Groups[0].Rules[0].Labels[cpromv1.SeverityLabel],
				AlertTemplateDetail: string(vmRuleBytes), // 此时的vmRuleBytes中不包含Create后m.Metadata中新填充的resourceVersion、selfLink等字段，以后也不会包含
				Version:             "v1",
				Operator:            userName,
			}
			//同名的update
			if req.Override {
				alertTemplate, err := alert_template.QueryByName(rule.Alert, accountID)
				if err == nil {
					alertingTplRecordUpdate := model.AlertTemplate{
						BCEAccountID:        accountID,
						AlertTemplateID:     alertTemplate.AlertTemplateID,
						AlertTemplateName:   alertingTplRecord.AlertTemplateName,
						Level:               alertingTplRecord.Level,
						AlertTemplateDetail: string(vmRuleBytes), // 此时的vmRuleBytes中不包含Create后m.Metadata中新填充的resourceVersion、selfLink等字段，以后也不会包含
						Version:             incrementVersion(alertTemplate.Version),
						Operator:            userName,
					}
					err = alert_template.Update(alertingTplRecordUpdate)
					if err != nil {
						logger.Errorf(ctx, "[UpdateAlertingRule] to DB failed. errorInfo=[%v]", err.Error())
						res.Message = fmt.Sprintf("update record to DB failed.")
						//c.JSON(http.StatusInternalServerError, errorcodev2.InternalServerErrorWithMessage(requestID, res.Message))
					}
					result = append(result, alertTemplate.AlertTemplateID)
					continue
				}
			}

			_, err = alert_template.QueryByName(rule.Alert, accountID)
			if err == nil {
				newTemplateName, err := utils.GenerateNameForTpl(ctx, alertingTplRecord.AlertTemplateName+"_", h.config.ResourceClusterRegionID, accountID)
				if err != nil {
					res.Message = fmt.Sprintf("genNotifyRuleID err: %v", err)
					logger.Errorf(ctx, "%v", res.Message)
					c.JSON(http.StatusInternalServerError, errorcodev2.InternalServerErrorWithMessage(requestID, res.Message))
					return
				}
				alertingTplRecord.AlertTemplateName = newTemplateName
			}

			err = alert_template.Insert(alertingTplRecord)
			if err != nil {
				logger.Errorf(ctx, "[InsertAlertingRule] to DB failed. errorInfo=[%v]", err.Error())
				res.Message = fmt.Sprintf("insert record to DB failed.")
				//c.JSON(http.StatusInternalServerError, errorcodev2.InternalServerErrorWithMessage(requestID, res.Message))
				continue
			}
			result = append(result, templateId)
		}
	}

	response := ImportResponse{
		AlertTemplateIds: result,
	}
	c.JSON(http.StatusOK, response)
}

// TplBindInstance 绑定模板到实例
func (h *AlertingRuleAPI) TplBindInstance(ctx context.Context, templateID string, req ApplyTemplateRequest, accountID string, tmplDetail TemplateDetail) error {

	mapping, err := alert_template.QueryByInstanceAndTplID(templateID, req.InstanceID, accountID)
	if err != nil {
		logger.Errorf(ctx, "[Create alertTplMapping] to DB failed. errorInfo=[%v]", err.Error())
		return err
	}

	if mapping != nil && mapping.ID != 0 { // 检查主键是否存在
		logger.Errorf(ctx, "[InsertAlertingRule] duplicate entry for templateID=%s, instance=%s",
			templateID, req.InstanceID)
		return fmt.Errorf("The template has been bound to the instance.")
	}

	retryPolicy := retrypolicy.NewIntervalRetryPolicy(ctx, 3, 1)
	cpromRegion := getProcessedRegion(h.config.Region, req.Region)
	cpromClient := cprom.NewClient(&bce.Config{
		Checksum:    true,
		Timeout:     time.Second * 5,
		Region:      cpromRegion,
		Endpoint:    cprom.Endpoints[cpromRegion],
		RetryPolicy: retryPolicy},
	)

	alertRuleReq := cprom.CreateAlertRuleRequest{
		AlertName:    tmplDetail.AlertTemplateName,
		Expr:         tmplDetail.Expr,
		For:          tmplDetail.For,
		Description:  tmplDetail.Description,
		Severity:     tmplDetail.Level,
		NotifyRuleID: tmplDetail.NotifyRuleID,
	}

	if len(tmplDetail.Annotations) > 0 {
		alertRuleReq.Annotations = tmplDetail.Annotations
	}

	if len(tmplDetail.Labels) > 0 {
		alertRuleReq.Labels = tmplDetail.Labels
	}

	// 创建告警规则
	resp, err := cpromClient.CreateAlertingRule(ctx, req.InstanceID, &alertRuleReq, h.stsClient.NewSignOption(ctx, accountID))
	if err != nil {
		logger.Errorf(ctx, "create alerting rule err: %v", err)
		return err
	}

	//get instance name
	instanceResp, err := cpromClient.GetInstance(ctx, req.InstanceID, h.stsClient.NewSignOption(ctx, accountID))
	if err != nil {
		logger.Errorf(ctx, "get monitor instance err: %v", err)
		return err
	}
	instanceName := instanceResp.InstanceName

	alertID := resp.AlertID
	logger.Infof(ctx, "create alerting rule : %v", alertID)

	alertTplMapping := model.TemplateInstanceMapping{
		BCEAccountID:    accountID,
		AlertTemplateID: templateID,
		InstanceID:      req.InstanceID,
		InstanceName:    instanceName,
		AlertRuleID:     alertID, // 此时的vmRuleBytes中不包含Create后m.Metadata中新填充的resourceVersion、selfLink等字段，以后也不会包含
		Version:         tmplDetail.Version,
		Region:          req.Region,
		Operator:        "admin",
	}
	err = alert_template.InsertMapping(alertTplMapping)
	if err != nil {
		logger.Errorf(ctx, "[Insert alertTplMapping] to DB failed. errorInfo=[%v]", err.Error())
		return err
	}
	return nil
}

// TplSyncInstance 同步实例到指定模板，更新告警规则
func (h *AlertingRuleAPI) TplSyncInstance(ctx context.Context, templateID string, req ApplyTemplateRequest, accountID string, tmplDetail TemplateDetail) error {

	mapping, err := alert_template.QueryByInstanceAndTplID(templateID, req.InstanceID, accountID)
	if err != nil {
		logger.Errorf(ctx, "[Sync alertTplMapping] to DB failed. errorInfo=[%v]", err.Error())
		return err
	}

	if mapping == nil { // 检查主键是否不存在
		logger.Errorf(ctx, "[InsertAlertingRule] duplicate entry for templateID=%s, instance=%s",
			templateID, req.InstanceID)
		return fmt.Errorf("The template has been unbound from the instance.")
	}
	// 若版本一致不用更新

	retryPolicy := retrypolicy.NewIntervalRetryPolicy(ctx, 3, 1)
	cpromRegion := getProcessedRegion(h.config.Region, req.Region)
	cpromClient := cprom.NewClient(&bce.Config{
		Checksum:    true,
		Timeout:     time.Second * 5,
		Region:      cpromRegion,
		Endpoint:    cprom.Endpoints[cpromRegion],
		RetryPolicy: retryPolicy},
	)

	alertRuleReq := cprom.CreateAlertRuleRequest{
		AlertName:    tmplDetail.AlertTemplateName,
		Expr:         tmplDetail.Expr,
		For:          tmplDetail.For,
		Description:  tmplDetail.Description,
		Severity:     tmplDetail.Level,
		NotifyRuleID: tmplDetail.NotifyRuleID,
		Enable:       true,
	}

	if len(tmplDetail.Annotations) > 0 {
		alertRuleReq.Annotations = tmplDetail.Annotations
	}

	if len(tmplDetail.Labels) > 0 {
		alertRuleReq.Labels = tmplDetail.Labels
	}

	//get instance name
	instanceResp, err := cpromClient.GetInstance(ctx, req.InstanceID, h.stsClient.NewSignOption(ctx, accountID))
	if err != nil {
		logger.Errorf(ctx, "get monitor instance err: %v", err)
		return err
	}

	instanceName := instanceResp.InstanceName
	// 更新告警规则
	err = cpromClient.UpdateAlertingRule(ctx, req.InstanceID, mapping.AlertRuleID, &alertRuleReq, h.stsClient.NewSignOption(ctx, accountID))
	if err != nil {
		logger.Errorf(ctx, "create alerting rule err: %v", err)
		return err
	}

	alertTplMapping := model.TemplateInstanceMapping{
		BCEAccountID:    accountID,
		AlertTemplateID: templateID,
		InstanceID:      req.InstanceID,
		InstanceName:    instanceName,
		AlertRuleID:     mapping.AlertRuleID, // 此时的vmRuleBytes中不包含Create后m.Metadata中新填充的resourceVersion、selfLink等字段，以后也不会包含
		Version:         tmplDetail.Version,
		Region:          req.Region,
		Operator:        "admin",
	}
	err = alert_template.UpdateMapping(alertTplMapping)
	if err != nil {
		logger.Errorf(ctx, "[Insert alertTplMapping] to DB failed. errorInfo=[%v]", err.Error())
		return err
	}
	return nil
}

// 解绑实例与模板
func (h *AlertingRuleAPI) TplUnbindInstance(ctx context.Context, templateID string, req ApplyTemplateRequest, accountID string) error {

	mapping, err := alert_template.QueryByInstanceAndTplID(templateID, req.InstanceID, accountID)
	if err != nil {
		logger.Errorf(ctx, "[QueryByInstanceAndTplID] from DB failed=[%v]", err.Error())
		return fmt.Errorf("[QueryByInstanceAndTplID] from DB failed")
	}

	if mapping == nil { // 检查主键是否不存在
		logger.Errorf(ctx, "mapping is not exist for templateID=%s, instance=%s",
			templateID, req.InstanceID)
		return fmt.Errorf("The template has been unbound from the instance.")
	}

	err = alert_template.DeleteMapping(templateID, req.InstanceID, req.Region, accountID)
	if err != nil {
		logger.Errorf(ctx, "[Delete alertTplMapping] to DB failed. errorInfo=[%v]", err.Error())
		return err
	}
	return nil
}

// 定义递增版本号的函数
func incrementVersion(currentVersion string) string {
	// 去除前缀"v"，获取数字部分
	versionStr := strings.TrimPrefix(currentVersion, "v")
	// 转换为整数
	versionNum, err := strconv.Atoi(versionStr)
	if err != nil {
		return "v1"
	}
	// 递增并格式化为新版本号
	return fmt.Sprintf("v%d", versionNum+1)
}

// 处理region
func getProcessedRegion(cfgRegion, reqRegion string) string {
	// 如果配置region以test结尾，且请求region不以test结尾
	if strings.HasSuffix(cfgRegion, "test") {
		if strings.HasSuffix(reqRegion, "bj") || strings.HasSuffix(reqRegion, "bd") || strings.HasSuffix(reqRegion, "gz") {
			return reqRegion + "test"
		}

	}
	return reqRegion
}

// 检查新告警名称是否冲突
func checkSysAlertNameConflict(cfg *configuration.ServiceConfig, newName string) bool {
	existing := make(map[string]bool)
	listTemplates := cfg.AlertTemplate()
	for _, template := range listTemplates.Rules {
		existing[template.Alert] = true
	}

	if existing[newName] {
		return true
	}
	return false
}

// 删除内置标签及注解
func removeKeys(m map[string]string, keys ...string) {
	for _, key := range keys {
		delete(m, key)
	}
}
