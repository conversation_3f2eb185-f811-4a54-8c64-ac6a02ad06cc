package alerting_rule

import (
	"encoding/json"
	"fmt"
	"time"

	v1 "icode.baidu.com/baidu/cprom/cloud-stack/pkg/apis/cprom/v1"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/mysql"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/mysql/model"
)

var timeTemplate = "2006-01-02T15:04:05Z" // time.Time转换为string的模板格式

func Insert(alertingRuleRecord model.AlertingRule) (err error) {
	alertingRuleRecord.UpdateTime = time.Now() // 无论数据值是否更新，都将update_time更新为当前时间
	result := mysql.Conn.Create(&alertingRuleRecord)
	if result.Error != nil {
		err = result.Error
		return
	}
	return
}

func Update(alertingRuleRecord model.AlertingRule) (err error) {
	alertingRuleRecord.UpdateTime = time.Now() // 无论数据值是否更新，都将update_time更新为当前时间
	result := mysql.Conn.Model(&model.AlertingRule{}).
		Where("alerting_rule_id = ?", alertingRuleRecord.AlertingRuleID).
		Where("new_instance_id = ?", alertingRuleRecord.NewInstanceID).
		Where("is_delete = ?", 0).
		Updates(&alertingRuleRecord)
	if result.Error != nil {
		err = result.Error
		return
	}
	return
}

func QueryByName(alertingRuleName string, newInstanceId string) (alertingRule v1.AlertingRule, err error) {
	var alertingRuleRecord model.AlertingRule
	result := mysql.Conn.Model(&model.AlertingRule{}).
		Where("alerting_rule_name = ?", alertingRuleName).
		Where("new_instance_id = ?", newInstanceId).
		Find(&alertingRuleRecord)
	if result.Error != nil {
		err = result.Error
		return
	}
	return
}

func QueryById(alertingRuleId string, newInstanceId string) (alertingRuleRecord model.AlertingRule, err error) {
	result := mysql.Conn.Model(&model.AlertingRule{}).
		Where("alerting_rule_id = ?", alertingRuleId).
		Where("new_instance_id = ?", newInstanceId).
		Where("is_delete = ?", 0).
		Find(&alertingRuleRecord)
	if result.Error != nil {
		err = result.Error
		return
	}
	return
}

func CountById(newInstanceId string) (count int, err error) {
	db := mysql.Conn.Model(&model.AlertingRule{}).
		Where("new_instance_id = ?", newInstanceId).
		Where("is_delete = ?", 0)
	var totalCount int
	db.Count(&totalCount)
	if db.Error != nil {
		err = db.Error
		return
	}
	return totalCount, nil
}

func Delete(alertingRuleId string, newInstanceId string) (err error) {
	result := mysql.Conn.Model(&model.AlertingRule{}).
		Where("alerting_rule_id = ?", alertingRuleId).
		Where("new_instance_id = ?", newInstanceId).
		Update("is_delete", 1)
	if result.Error != nil {
		err = result.Error
		return
	}
	return
}

func QueryLimitByPageSize(alertingRuleName string, newInstanceId string, pageSize int64, pageNo int64) (int, []model.AlertingRule, error) {
	var alertingRuleRecords []model.AlertingRule
	db := mysql.Conn.Model(&model.AlertingRule{}).
		Where(fmt.Sprintf("alerting_rule_name like %q", ("%"+alertingRuleName+"%"))).
		Where("new_instance_id = ?", newInstanceId).
		Where("is_delete = ?", 0)
	var totalCount int
	db.Count(&totalCount)
	db.Offset((pageNo - 1) * pageSize).Limit(pageSize).Order("update_time desc").Find(&alertingRuleRecords)
	err := db.Error

	return totalCount, alertingRuleRecords, err
}

func BatchDelete(newInstanceId string, accountId string) (err error) {
	result := mysql.Conn.Model(&model.AlertingRule{}).
		Where("bce_account_id = ?", accountId).
		Where("new_instance_id = ?", newInstanceId).
		Update("is_delete", 1)
	if result.Error != nil {
		err = result.Error
		return
	}
	return
}

// BatchUpdateEnable 批量更新告警规则的启用状态
func BatchUpdateEnable(alertingRuleIds []string, newInstanceId string, accountId string, enable bool) (err error) {
	if len(alertingRuleIds) == 0 {
		return nil
	}

	enableStr := "false"
	if enable {
		enableStr = "true"
	}

	// 批量查询需要更新的记录
	var alertingRules []model.AlertingRule
	result := mysql.Conn.Model(&model.AlertingRule{}).
		Where("alerting_rule_id IN ?", alertingRuleIds).
		Where("new_instance_id = ?", newInstanceId).
		Where("bce_account_id = ?", accountId).
		Where("is_delete = ?", 0).
		Find(&alertingRules)

	if result.Error != nil {
		return result.Error
	}

	// 批量更新每条记录的alerting_rule_detail字段
	for _, rule := range alertingRules {
		// 解析JSON并更新enable字段
		var vmRule map[string]interface{}
		if err := json.Unmarshal([]byte(rule.AlertingRuleDetail), &vmRule); err != nil {
			continue // 跳过解析失败的记录
		}

		// 更新metadata.labels.enable字段
		if metadata, ok := vmRule["metadata"].(map[string]interface{}); ok {
			if labels, ok := metadata["labels"].(map[string]interface{}); ok {
				labels["enable"] = enableStr
			}
		}

		// 将更新后的JSON序列化回去
		updatedJSON, err := json.Marshal(vmRule)
		if err != nil {
			continue // 跳过序列化失败的记录
		}

		// 更新数据库记录
		updateResult := mysql.Conn.Model(&model.AlertingRule{}).
			Where("alerting_rule_id = ?", rule.AlertingRuleID).
			Where("new_instance_id = ?", newInstanceId).
			Where("bce_account_id = ?", accountId).
			Where("is_delete = ?", 0).
			Updates(map[string]interface{}{
				"alerting_rule_detail": string(updatedJSON),
				"update_time":          time.Now(),
			})

		if updateResult.Error != nil {
			return updateResult.Error
		}
	}

	return nil
}

/*
func ConvertModel2Protocol(alertingRuleRecord model.AlertingRule) (alertingRule v1.AlertingRule) {

	alertingRule.MonitorInstanceId = alertingRuleRecord.NewInstanceID
	alertingRule.AlertingRuleID = alertingRuleRecord.AlertingRuleID
	alertingRule.AlertingRuleName = alertingRuleRecord.AlertingRuleName
	alertingRule.Expr = alertingRuleRecord.Expr
	alertingRule.Duration = alertingRuleRecord.For

	alertingRule.Severity = ""  // todo
	alertingRule.Description = ""  // todo

	isEnable := true
	if alertingRuleRecord.Enable == 0 {
		isEnable = false
	}
	alertingRule.IsEnabled = isEnable
	alertingRule.Operator = alertingRuleRecord.Operator

	err := json.Unmarshal([]byte(alertingRuleRecord.Labels), &alertingRule.Labels)
	if err != nil {
		zap.S().Errorf("JsonUnmarshal for alertingRuleRecord.Labels failed. errorInfo=[%v]", err.Error())
	}
	err = json.Unmarshal([]byte(alertingRuleRecord.Annotations), &alertingRule.Annotations)
	if err != nil {
		zap.S().Errorf("JsonUnmarshal for alertingRuleRecord.Annotations failed. errorInfo=[%v]", err.Error())
	}
	alertingRule.UpdateTime = int(alertingRuleRecord.UpdateTime.Local().Unix())  // 从time.Time -> 时间戳
	return
}
*/
