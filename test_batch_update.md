# 批量启停告警规则功能测试

## 功能概述
实现了批量启停告警规则的REST API接口，支持通过一个请求同时启动或停止多个告警规则。

## API接口

### 请求方式
```
PUT /v2/alerting_rule/{instanceId}/batch_update?action=start
PUT /v2/alerting_rule/{instanceId}/batch_update?action=stop
```

### 请求参数
- **路径参数**:
  - `instanceId`: Cprom监控实例ID

- **查询参数**:
  - `action`: 操作类型，支持 "start" 或 "stop"

- **请求体**:
```json
{
  "alertingRuleIds": ["rule-id-1", "rule-id-2", "rule-id-3"]
}
```

### 响应示例

#### 成功响应
```json
{
  "message": "successfully started 3 alerting rules",
  "count": 3
}
```

#### 部分成功响应
```json
{
  "message": "batch update partially successful: 2 succeeded, 1 failed. Failed rules: [rule-name-3]",
  "successCount": 2,
  "failedCount": 1,
  "failedRules": ["rule-name-3"]
}
```

#### 错误响应
```json
{
  "message": "action parameter must be 'start' or 'stop'",
  "requestId": "xxx"
}
```

## 实现细节

### 1. 数据库批量更新
- 新增 `BatchUpdateEnable` 函数在 `pkg/mysql/alerting_rule/alerting_rule_service.go`
- 批量查询需要更新的告警规则记录
- 解析并更新每条记录的 `alerting_rule_detail` JSON字段中的 `enable` 标签
- 使用事务确保数据一致性

### 2. Kubernetes CRD批量更新
- 使用label selector查询相关的VMRule对象
- 过滤出需要更新的规则（根据alertingRuleIds）
- 使用Strategic Merge Patch只更新 `enable` 标签，避免全量更新
- 并发处理多个VMRule对象的更新操作
- 记录成功和失败的规则，提供详细的反馈

### 3. 性能优化
- **数据库层面**: 使用批量数据库操作减少DB调用次数
- **K8s API层面**:
  - 使用label selector高效查询K8s资源
  - 使用Strategic Merge Patch替代全量Update，只更新必要字段
  - 并发处理多个VMRule对象，显著提升批量更新性能
  - 使用原子操作和互斥锁确保并发安全

### 4. 错误处理
- 参数验证：检查action参数和alertingRuleIds数组
- 数据库错误处理：记录详细错误信息
- K8s API错误处理：记录失败的规则名称
- 部分成功场景：返回成功和失败的统计信息

## 测试用例

### 1. 正常启动规则
```bash
curl -X PUT "http://localhost:8080/v2/alerting_rule/cprom-instance-1/batch_update?action=start" \
  -H "Content-Type: application/json" \
  -d '{"alertingRuleIds": ["rule-1", "rule-2"]}'
```

### 2. 正常停止规则
```bash
curl -X PUT "http://localhost:8080/v2/alerting_rule/cprom-instance-1/batch_update?action=stop" \
  -H "Content-Type: application/json" \
  -d '{"alertingRuleIds": ["rule-1", "rule-2"]}'
```

### 3. 参数错误测试
```bash
curl -X PUT "http://localhost:8080/v2/alerting_rule/cprom-instance-1/batch_update?action=invalid" \
  -H "Content-Type: application/json" \
  -d '{"alertingRuleIds": ["rule-1"]}'
```

### 4. 空规则列表测试
```bash
curl -X PUT "http://localhost:8080/v2/alerting_rule/cprom-instance-1/batch_update?action=start" \
  -H "Content-Type: application/json" \
  -d '{"alertingRuleIds": []}'
```

## 代码变更总结

### 新增文件
无

### 修改文件
1. `pkg/mysql/alerting_rule/alerting_rule_service.go`
   - 新增 `BatchUpdateEnable` 函数

2. `services/cprom-service/handler/alerting_rule/alert_openapi.go`
   - 完善 `BatchUpdate` 函数实现

3. `services/cprom-service/handler/alerting_rule/handler.go`
   - 修正路由注册路径

### 主要功能
- 支持批量启停告警规则
- 同时更新数据库和Kubernetes CRD
- 使用Strategic Merge Patch优化K8s更新性能
- 并发处理多个VMRule对象更新
- 提供详细的成功/失败反馈
- 优化性能，减少API调用次数

### 技术亮点
1. **Strategic Merge Patch**: 只更新VMRule对象的labels.enable字段，避免全量对象更新
2. **并发处理**: 使用goroutine并发更新多个K8s对象，提升批量操作性能
3. **原子操作**: 使用sync/atomic确保并发计数的线程安全
4. **互斥锁**: 使用sync.Mutex保护共享的failedRules切片
5. **批量数据库操作**: 一次性查询和更新多条数据库记录
